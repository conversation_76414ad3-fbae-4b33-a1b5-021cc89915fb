2025-07-28 20:31:06,750 - INFO - ========== 字幕 #11 处理开始 ==========
2025-07-28 20:31:06,750 - INFO - 字幕内容: 为了赚钱养家，女孩不顾母亲和奶奶的劝阻，毅然决定上山打猎。
2025-07-28 20:31:06,750 - INFO - 字幕序号: [799, 806]
2025-07-28 20:31:06,750 - INFO - 音频文件详情:
2025-07-28 20:31:06,750 - INFO -   - 路径: output\11.wav
2025-07-28 20:31:06,750 - INFO -   - 时长: 3.67秒
2025-07-28 20:31:06,751 - INFO -   - 验证音频时长: 3.67秒
2025-07-28 20:31:06,751 - INFO - 字幕时间戳信息:
2025-07-28 20:31:06,751 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:06,751 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:06,751 - INFO -   - 根据生成的音频时长(3.67秒)已调整字幕时间戳
2025-07-28 20:31:06,751 - INFO - ========== 开始为字幕 #11 生成 6 套场景方案 ==========
2025-07-28 20:31:06,751 - INFO - 开始查找字幕序号 [799, 806] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:06,751 - INFO - 找到related_overlap场景: scene_id=858, 字幕#799
2025-07-28 20:31:06,751 - INFO - 找到related_overlap场景: scene_id=859, 字幕#799
2025-07-28 20:31:06,751 - INFO - 找到related_overlap场景: scene_id=865, 字幕#806
2025-07-28 20:31:06,752 - INFO - 字幕 #799 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:06,752 - INFO - 字幕 #806 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:06,752 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:06,752 - INFO - 开始生成方案 #1
2025-07-28 20:31:06,752 - INFO - 方案 #1: 为字幕#799选择初始化overlap场景id=859
2025-07-28 20:31:06,752 - INFO - 方案 #1: 为字幕#806选择初始化overlap场景id=865
2025-07-28 20:31:06,752 - INFO - 方案 #1: 初始选择后，当前总时长=5.64秒
2025-07-28 20:31:06,752 - INFO - 方案 #1: 额外between选择后，当前总时长=5.64秒
2025-07-28 20:31:06,752 - INFO - 方案 #1: 场景总时长(5.64秒)大于音频时长(3.67秒)，需要裁剪
2025-07-28 20:31:06,752 - INFO - 调整前总时长: 5.64秒, 目标时长: 3.67秒
2025-07-28 20:31:06,752 - INFO - 需要裁剪 1.97秒
2025-07-28 20:31:06,752 - INFO - 裁剪最长场景ID=865：从4.36秒裁剪至2.39秒
2025-07-28 20:31:06,752 - INFO - 调整后总时长: 3.67秒，与目标时长差异: 0.00秒
2025-07-28 20:31:06,752 - INFO - 方案 #1 调整/填充后最终总时长: 3.67秒
2025-07-28 20:31:06,752 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:06,752 - INFO - 开始生成方案 #2
2025-07-28 20:31:06,752 - INFO - 方案 #2: 为字幕#799选择初始化overlap场景id=858
2025-07-28 20:31:06,752 - INFO - 方案 #2: 初始选择后，当前总时长=1.16秒
2025-07-28 20:31:06,752 - INFO - 方案 #2: 额外between选择后，当前总时长=1.16秒
2025-07-28 20:31:06,752 - INFO - 方案 #2: 场景总时长(1.16秒)小于音频时长(3.67秒)，需要延伸填充
2025-07-28 20:31:06,752 - INFO - 方案 #2: 最后一个场景ID: 858
2025-07-28 20:31:06,752 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 857
2025-07-28 20:31:06,752 - INFO - 方案 #2: 需要填充时长: 2.51秒
2025-07-28 20:31:06,752 - INFO - 方案 #2: 跳过已使用的场景: scene_id=859
2025-07-28 20:31:06,752 - INFO - 方案 #2: 追加场景 scene_id=860 (完整时长 0.72秒)
2025-07-28 20:31:06,752 - INFO - 方案 #2: 追加场景 scene_id=861 (裁剪至 1.79秒)
2025-07-28 20:31:06,752 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:06,752 - INFO - 方案 #2 调整/填充后最终总时长: 3.67秒
2025-07-28 20:31:06,752 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:06,752 - INFO - 开始生成方案 #3
2025-07-28 20:31:06,752 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,752 - INFO - 开始生成方案 #4
2025-07-28 20:31:06,752 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,752 - INFO - 开始生成方案 #5
2025-07-28 20:31:06,753 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,753 - INFO - 开始生成方案 #6
2025-07-28 20:31:06,753 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,753 - INFO - ========== 字幕 #11 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:06,753 - INFO - 
----- 处理字幕 #11 的方案 #1 -----
2025-07-28 20:31:06,753 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-28 20:31:06,753 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsr4s2k_c
2025-07-28 20:31:06,753 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\859.mp4 (确认存在: True)
2025-07-28 20:31:06,753 - INFO - 添加场景ID=859，时长=1.28秒，累计时长=1.28秒
2025-07-28 20:31:06,753 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\865.mp4 (确认存在: True)
2025-07-28 20:31:06,753 - INFO - 添加场景ID=865，时长=4.36秒，累计时长=5.64秒
2025-07-28 20:31:06,753 - INFO - 场景总时长(5.64秒)已达到音频时长(3.67秒)的1.5倍，停止添加场景
2025-07-28 20:31:06,754 - INFO - 准备合并 2 个场景文件，总时长约 5.64秒
2025-07-28 20:31:06,754 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/859.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/865.mp4'

2025-07-28 20:31:06,754 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsr4s2k_c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsr4s2k_c\temp_combined.mp4
2025-07-28 20:31:06,880 - INFO - 合并后的视频时长: 5.69秒，目标音频时长: 3.67秒
2025-07-28 20:31:06,880 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsr4s2k_c\temp_combined.mp4 -ss 0 -to 3.67 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-28 20:31:07,138 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:07,138 - INFO - 目标音频时长: 3.67秒
2025-07-28 20:31:07,138 - INFO - 实际视频时长: 3.70秒
2025-07-28 20:31:07,138 - INFO - 时长差异: 0.03秒 (0.90%)
2025-07-28 20:31:07,138 - INFO - ==========================================
2025-07-28 20:31:07,138 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:07,138 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-28 20:31:07,139 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsr4s2k_c
2025-07-28 20:31:07,179 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:07,179 - INFO -   - 音频时长: 3.67秒
2025-07-28 20:31:07,179 - INFO -   - 视频时长: 3.70秒
2025-07-28 20:31:07,179 - INFO -   - 时长差异: 0.03秒 (0.90%)
2025-07-28 20:31:07,179 - INFO - 
----- 处理字幕 #11 的方案 #2 -----
2025-07-28 20:31:07,179 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-28 20:31:07,180 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgtfeej5c
2025-07-28 20:31:07,180 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\858.mp4 (确认存在: True)
2025-07-28 20:31:07,180 - INFO - 添加场景ID=858，时长=1.16秒，累计时长=1.16秒
2025-07-28 20:31:07,180 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\860.mp4 (确认存在: True)
2025-07-28 20:31:07,180 - INFO - 添加场景ID=860，时长=0.72秒，累计时长=1.88秒
2025-07-28 20:31:07,180 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\861.mp4 (确认存在: True)
2025-07-28 20:31:07,180 - INFO - 添加场景ID=861，时长=1.96秒，累计时长=3.84秒
2025-07-28 20:31:07,181 - INFO - 准备合并 3 个场景文件，总时长约 3.84秒
2025-07-28 20:31:07,181 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/858.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/860.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/861.mp4'

2025-07-28 20:31:07,181 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgtfeej5c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgtfeej5c\temp_combined.mp4
2025-07-28 20:31:07,326 - INFO - 合并后的视频时长: 3.91秒，目标音频时长: 3.67秒
2025-07-28 20:31:07,326 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgtfeej5c\temp_combined.mp4 -ss 0 -to 3.67 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-28 20:31:07,601 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:07,601 - INFO - 目标音频时长: 3.67秒
2025-07-28 20:31:07,601 - INFO - 实际视频时长: 3.70秒
2025-07-28 20:31:07,601 - INFO - 时长差异: 0.03秒 (0.90%)
2025-07-28 20:31:07,601 - INFO - ==========================================
2025-07-28 20:31:07,601 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:07,601 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-28 20:31:07,602 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgtfeej5c
2025-07-28 20:31:07,646 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:07,646 - INFO -   - 音频时长: 3.67秒
2025-07-28 20:31:07,646 - INFO -   - 视频时长: 3.70秒
2025-07-28 20:31:07,646 - INFO -   - 时长差异: 0.03秒 (0.90%)
2025-07-28 20:31:07,646 - INFO - 
字幕 #11 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:07,646 - INFO - 生成的视频文件:
2025-07-28 20:31:07,646 - INFO -   1. F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-07-28 20:31:07,646 - INFO -   2. F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-07-28 20:31:07,646 - INFO - ========== 字幕 #11 处理结束 ==========

