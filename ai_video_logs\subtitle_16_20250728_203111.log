2025-07-28 20:31:11,672 - INFO - ========== 字幕 #16 处理开始 ==========
2025-07-28 20:31:11,672 - INFO - 字幕内容: 猎人们不信这是一个女孩的战绩，但事实胜于雄辩。
2025-07-28 20:31:11,672 - INFO - 字幕序号: [1095, 1097]
2025-07-28 20:31:11,672 - INFO - 音频文件详情:
2025-07-28 20:31:11,672 - INFO -   - 路径: output\16.wav
2025-07-28 20:31:11,672 - INFO -   - 时长: 2.82秒
2025-07-28 20:31:11,672 - INFO -   - 验证音频时长: 2.82秒
2025-07-28 20:31:11,672 - INFO - 字幕时间戳信息:
2025-07-28 20:31:11,672 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:11,672 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:11,672 - INFO -   - 根据生成的音频时长(2.82秒)已调整字幕时间戳
2025-07-28 20:31:11,672 - INFO - ========== 开始为字幕 #16 生成 6 套场景方案 ==========
2025-07-28 20:31:11,672 - INFO - 开始查找字幕序号 [1095, 1097] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:11,673 - INFO - 找到related_overlap场景: scene_id=1172, 字幕#1095
2025-07-28 20:31:11,673 - INFO - 找到related_overlap场景: scene_id=1173, 字幕#1095
2025-07-28 20:31:11,673 - INFO - 找到related_overlap场景: scene_id=1176, 字幕#1097
2025-07-28 20:31:11,673 - INFO - 找到related_between场景: scene_id=1174, 字幕#1095
2025-07-28 20:31:11,673 - INFO - 找到related_between场景: scene_id=1175, 字幕#1095
2025-07-28 20:31:11,674 - INFO - 字幕 #1095 找到 2 个overlap场景, 2 个between场景
2025-07-28 20:31:11,674 - INFO - 字幕 #1097 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:11,674 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-07-28 20:31:11,674 - INFO - 开始生成方案 #1
2025-07-28 20:31:11,674 - INFO - 方案 #1: 为字幕#1095选择初始化overlap场景id=1173
2025-07-28 20:31:11,674 - INFO - 方案 #1: 为字幕#1097选择初始化overlap场景id=1176
2025-07-28 20:31:11,674 - INFO - 方案 #1: 初始选择后，当前总时长=4.48秒
2025-07-28 20:31:11,674 - INFO - 方案 #1: 额外between选择后，当前总时长=4.48秒
2025-07-28 20:31:11,674 - INFO - 方案 #1: 场景总时长(4.48秒)大于音频时长(2.82秒)，需要裁剪
2025-07-28 20:31:11,674 - INFO - 调整前总时长: 4.48秒, 目标时长: 2.82秒
2025-07-28 20:31:11,674 - INFO - 需要裁剪 1.66秒
2025-07-28 20:31:11,674 - INFO - 裁剪最长场景ID=1176：从3.24秒裁剪至1.58秒
2025-07-28 20:31:11,674 - INFO - 调整后总时长: 2.82秒，与目标时长差异: 0.00秒
2025-07-28 20:31:11,674 - INFO - 方案 #1 调整/填充后最终总时长: 2.82秒
2025-07-28 20:31:11,674 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:11,674 - INFO - 开始生成方案 #2
2025-07-28 20:31:11,674 - INFO - 方案 #2: 为字幕#1095选择初始化overlap场景id=1172
2025-07-28 20:31:11,674 - INFO - 方案 #2: 初始选择后，当前总时长=1.28秒
2025-07-28 20:31:11,674 - INFO - 方案 #2: 额外between选择后，当前总时长=1.28秒
2025-07-28 20:31:11,674 - INFO - 方案 #2: 额外添加between场景id=1175, 当前总时长=2.40秒
2025-07-28 20:31:11,674 - INFO - 方案 #2: 额外添加between场景id=1174, 当前总时长=4.52秒
2025-07-28 20:31:11,674 - INFO - 方案 #2: 场景总时长(4.52秒)大于音频时长(2.82秒)，需要裁剪
2025-07-28 20:31:11,674 - INFO - 调整前总时长: 4.52秒, 目标时长: 2.82秒
2025-07-28 20:31:11,674 - INFO - 需要裁剪 1.70秒
2025-07-28 20:31:11,674 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:11,674 - INFO - 裁剪场景ID=1174：从2.12秒裁剪至1.00秒
2025-07-28 20:31:11,674 - INFO - 裁剪场景ID=1172：从1.28秒裁剪至1.00秒
2025-07-28 20:31:11,674 - INFO - 裁剪场景ID=1175：从1.12秒裁剪至1.00秒
2025-07-28 20:31:11,674 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.18秒
2025-07-28 20:31:11,674 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.18秒
2025-07-28 20:31:11,674 - INFO - 方案 #2 调整/填充后最终总时长: 3.00秒
2025-07-28 20:31:11,674 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:11,674 - INFO - 开始生成方案 #3
2025-07-28 20:31:11,674 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,674 - INFO - 开始生成方案 #4
2025-07-28 20:31:11,674 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,674 - INFO - 开始生成方案 #5
2025-07-28 20:31:11,674 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,674 - INFO - 开始生成方案 #6
2025-07-28 20:31:11,674 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,674 - INFO - ========== 字幕 #16 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:11,674 - INFO - 
----- 处理字幕 #16 的方案 #1 -----
2025-07-28 20:31:11,674 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-28 20:31:11,675 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgqda5uvi
2025-07-28 20:31:11,675 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1173.mp4 (确认存在: True)
2025-07-28 20:31:11,675 - INFO - 添加场景ID=1173，时长=1.24秒，累计时长=1.24秒
2025-07-28 20:31:11,675 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1176.mp4 (确认存在: True)
2025-07-28 20:31:11,675 - INFO - 添加场景ID=1176，时长=3.24秒，累计时长=4.48秒
2025-07-28 20:31:11,675 - INFO - 场景总时长(4.48秒)已达到音频时长(2.82秒)的1.5倍，停止添加场景
2025-07-28 20:31:11,675 - INFO - 准备合并 2 个场景文件，总时长约 4.48秒
2025-07-28 20:31:11,676 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1173.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1176.mp4'

2025-07-28 20:31:11,676 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgqda5uvi\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgqda5uvi\temp_combined.mp4
2025-07-28 20:31:11,800 - INFO - 合并后的视频时长: 4.53秒，目标音频时长: 2.82秒
2025-07-28 20:31:11,800 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgqda5uvi\temp_combined.mp4 -ss 0 -to 2.823 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-28 20:31:12,042 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:12,042 - INFO - 目标音频时长: 2.82秒
2025-07-28 20:31:12,042 - INFO - 实际视频时长: 2.86秒
2025-07-28 20:31:12,042 - INFO - 时长差异: 0.04秒 (1.42%)
2025-07-28 20:31:12,042 - INFO - ==========================================
2025-07-28 20:31:12,042 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:12,042 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-28 20:31:12,042 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgqda5uvi
2025-07-28 20:31:12,084 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:12,084 - INFO -   - 音频时长: 2.82秒
2025-07-28 20:31:12,084 - INFO -   - 视频时长: 2.86秒
2025-07-28 20:31:12,085 - INFO -   - 时长差异: 0.04秒 (1.42%)
2025-07-28 20:31:12,085 - INFO - 
----- 处理字幕 #16 的方案 #2 -----
2025-07-28 20:31:12,085 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-28 20:31:12,085 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp984y0nxt
2025-07-28 20:31:12,085 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1172.mp4 (确认存在: True)
2025-07-28 20:31:12,085 - INFO - 添加场景ID=1172，时长=1.28秒，累计时长=1.28秒
2025-07-28 20:31:12,086 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1175.mp4 (确认存在: True)
2025-07-28 20:31:12,086 - INFO - 添加场景ID=1175，时长=1.12秒，累计时长=2.40秒
2025-07-28 20:31:12,086 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1174.mp4 (确认存在: True)
2025-07-28 20:31:12,086 - INFO - 添加场景ID=1174，时长=2.12秒，累计时长=4.52秒
2025-07-28 20:31:12,086 - INFO - 场景总时长(4.52秒)已达到音频时长(2.82秒)的1.5倍，停止添加场景
2025-07-28 20:31:12,086 - INFO - 准备合并 3 个场景文件，总时长约 4.52秒
2025-07-28 20:31:12,086 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1172.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1175.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1174.mp4'

2025-07-28 20:31:12,086 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp984y0nxt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp984y0nxt\temp_combined.mp4
2025-07-28 20:31:12,286 - INFO - 合并后的视频时长: 4.59秒，目标音频时长: 2.82秒
2025-07-28 20:31:12,286 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp984y0nxt\temp_combined.mp4 -ss 0 -to 2.823 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-28 20:31:12,563 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:12,563 - INFO - 目标音频时长: 2.82秒
2025-07-28 20:31:12,563 - INFO - 实际视频时长: 2.86秒
2025-07-28 20:31:12,563 - INFO - 时长差异: 0.04秒 (1.42%)
2025-07-28 20:31:12,563 - INFO - ==========================================
2025-07-28 20:31:12,563 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:12,563 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-28 20:31:12,565 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp984y0nxt
2025-07-28 20:31:12,607 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:12,607 - INFO -   - 音频时长: 2.82秒
2025-07-28 20:31:12,607 - INFO -   - 视频时长: 2.86秒
2025-07-28 20:31:12,607 - INFO -   - 时长差异: 0.04秒 (1.42%)
2025-07-28 20:31:12,608 - INFO - 
字幕 #16 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:12,608 - INFO - 生成的视频文件:
2025-07-28 20:31:12,608 - INFO -   1. F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-07-28 20:31:12,608 - INFO -   2. F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-07-28 20:31:12,608 - INFO - ========== 字幕 #16 处理结束 ==========

