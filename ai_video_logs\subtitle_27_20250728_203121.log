2025-07-28 20:31:21,441 - INFO - ========== 字幕 #27 处理开始 ==========
2025-07-28 20:31:21,441 - INFO - 字幕内容: 女孩冷笑一声，让他自己去扛，扛得动熊就是他的。
2025-07-28 20:31:21,441 - INFO - 字幕序号: [2032, 2035]
2025-07-28 20:31:21,441 - INFO - 音频文件详情:
2025-07-28 20:31:21,441 - INFO -   - 路径: output\27.wav
2025-07-28 20:31:21,441 - INFO -   - 时长: 2.57秒
2025-07-28 20:31:21,441 - INFO -   - 验证音频时长: 2.57秒
2025-07-28 20:31:21,442 - INFO - 字幕时间戳信息:
2025-07-28 20:31:21,442 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:21,442 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:21,442 - INFO -   - 根据生成的音频时长(2.57秒)已调整字幕时间戳
2025-07-28 20:31:21,442 - INFO - ========== 开始为字幕 #27 生成 6 套场景方案 ==========
2025-07-28 20:31:21,442 - INFO - 开始查找字幕序号 [2032, 2035] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:21,442 - INFO - 找到related_overlap场景: scene_id=2122, 字幕#2032
2025-07-28 20:31:21,442 - INFO - 找到related_overlap场景: scene_id=2125, 字幕#2035
2025-07-28 20:31:21,444 - INFO - 字幕 #2032 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:21,444 - INFO - 字幕 #2035 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:21,444 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:21,444 - INFO - 开始生成方案 #1
2025-07-28 20:31:21,444 - INFO - 方案 #1: 为字幕#2032选择初始化overlap场景id=2122
2025-07-28 20:31:21,444 - INFO - 方案 #1: 为字幕#2035选择初始化overlap场景id=2125
2025-07-28 20:31:21,444 - INFO - 方案 #1: 初始选择后，当前总时长=3.16秒
2025-07-28 20:31:21,444 - INFO - 方案 #1: 额外between选择后，当前总时长=3.16秒
2025-07-28 20:31:21,444 - INFO - 方案 #1: 场景总时长(3.16秒)大于音频时长(2.57秒)，需要裁剪
2025-07-28 20:31:21,444 - INFO - 调整前总时长: 3.16秒, 目标时长: 2.57秒
2025-07-28 20:31:21,444 - INFO - 需要裁剪 0.59秒
2025-07-28 20:31:21,444 - INFO - 裁剪最长场景ID=2122：从2.00秒裁剪至1.41秒
2025-07-28 20:31:21,444 - INFO - 调整后总时长: 2.57秒，与目标时长差异: 0.00秒
2025-07-28 20:31:21,444 - INFO - 方案 #1 调整/填充后最终总时长: 2.57秒
2025-07-28 20:31:21,444 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:21,444 - INFO - 开始生成方案 #2
2025-07-28 20:31:21,444 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:21,444 - INFO - 开始生成方案 #3
2025-07-28 20:31:21,444 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:21,444 - INFO - 开始生成方案 #4
2025-07-28 20:31:21,444 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:21,444 - INFO - 开始生成方案 #5
2025-07-28 20:31:21,444 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:21,444 - INFO - 开始生成方案 #6
2025-07-28 20:31:21,444 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:21,444 - INFO - ========== 字幕 #27 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:21,444 - INFO - 
----- 处理字幕 #27 的方案 #1 -----
2025-07-28 20:31:21,444 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-28 20:31:21,444 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqcf9vuy9
2025-07-28 20:31:21,445 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2122.mp4 (确认存在: True)
2025-07-28 20:31:21,445 - INFO - 添加场景ID=2122，时长=2.00秒，累计时长=2.00秒
2025-07-28 20:31:21,445 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2125.mp4 (确认存在: True)
2025-07-28 20:31:21,445 - INFO - 添加场景ID=2125，时长=1.16秒，累计时长=3.16秒
2025-07-28 20:31:21,445 - INFO - 准备合并 2 个场景文件，总时长约 3.16秒
2025-07-28 20:31:21,445 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2122.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2125.mp4'

2025-07-28 20:31:21,445 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqcf9vuy9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqcf9vuy9\temp_combined.mp4
2025-07-28 20:31:21,563 - INFO - 合并后的视频时长: 3.21秒，目标音频时长: 2.57秒
2025-07-28 20:31:21,563 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqcf9vuy9\temp_combined.mp4 -ss 0 -to 2.573 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-28 20:31:21,792 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:21,792 - INFO - 目标音频时长: 2.57秒
2025-07-28 20:31:21,794 - INFO - 实际视频时长: 2.62秒
2025-07-28 20:31:21,794 - INFO - 时长差异: 0.05秒 (1.94%)
2025-07-28 20:31:21,794 - INFO - ==========================================
2025-07-28 20:31:21,794 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:21,794 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-28 20:31:21,794 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqcf9vuy9
2025-07-28 20:31:21,836 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:21,836 - INFO -   - 音频时长: 2.57秒
2025-07-28 20:31:21,836 - INFO -   - 视频时长: 2.62秒
2025-07-28 20:31:21,836 - INFO -   - 时长差异: 0.05秒 (1.94%)
2025-07-28 20:31:21,836 - INFO - 
字幕 #27 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:21,836 - INFO - 生成的视频文件:
2025-07-28 20:31:21,836 - INFO -   1. F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-07-28 20:31:21,836 - INFO - ========== 字幕 #27 处理结束 ==========

