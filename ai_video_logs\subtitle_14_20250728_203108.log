2025-07-28 20:31:08,992 - INFO - ========== 字幕 #14 处理开始 ==========
2025-07-28 20:31:08,992 - INFO - 字幕内容: 女孩开启猎物追踪术，轻松锁定了猎物的位置，让同行的猎户大叔目瞪口呆。
2025-07-28 20:31:08,992 - INFO - 字幕序号: [994, 1004]
2025-07-28 20:31:08,992 - INFO - 音频文件详情:
2025-07-28 20:31:08,992 - INFO -   - 路径: output\14.wav
2025-07-28 20:31:08,992 - INFO -   - 时长: 3.98秒
2025-07-28 20:31:08,994 - INFO -   - 验证音频时长: 3.98秒
2025-07-28 20:31:08,994 - INFO - 字幕时间戳信息:
2025-07-28 20:31:08,994 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:08,994 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:08,994 - INFO -   - 根据生成的音频时长(3.98秒)已调整字幕时间戳
2025-07-28 20:31:08,994 - INFO - ========== 开始为字幕 #14 生成 6 套场景方案 ==========
2025-07-28 20:31:08,994 - INFO - 开始查找字幕序号 [994, 1004] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:08,994 - INFO - 找到related_overlap场景: scene_id=1046, 字幕#994
2025-07-28 20:31:08,994 - INFO - 找到related_overlap场景: scene_id=1047, 字幕#994
2025-07-28 20:31:08,994 - INFO - 找到related_overlap场景: scene_id=1062, 字幕#1004
2025-07-28 20:31:08,994 - INFO - 找到related_overlap场景: scene_id=1064, 字幕#1004
2025-07-28 20:31:08,995 - INFO - 找到related_between场景: scene_id=1044, 字幕#994
2025-07-28 20:31:08,995 - INFO - 找到related_between场景: scene_id=1045, 字幕#994
2025-07-28 20:31:08,995 - INFO - 找到related_between场景: scene_id=1048, 字幕#994
2025-07-28 20:31:08,995 - INFO - 找到related_between场景: scene_id=1049, 字幕#994
2025-07-28 20:31:08,995 - INFO - 找到related_between场景: scene_id=1050, 字幕#994
2025-07-28 20:31:08,995 - INFO - 找到related_between场景: scene_id=1051, 字幕#994
2025-07-28 20:31:08,995 - INFO - 找到related_between场景: scene_id=1065, 字幕#1004
2025-07-28 20:31:08,996 - INFO - 字幕 #994 找到 2 个overlap场景, 6 个between场景
2025-07-28 20:31:08,996 - INFO - 字幕 #1004 找到 2 个overlap场景, 1 个between场景
2025-07-28 20:31:08,996 - INFO - 共收集 4 个未使用的overlap场景和 7 个未使用的between场景
2025-07-28 20:31:08,996 - INFO - 开始生成方案 #1
2025-07-28 20:31:08,996 - INFO - 方案 #1: 为字幕#994选择初始化overlap场景id=1046
2025-07-28 20:31:08,996 - INFO - 方案 #1: 为字幕#1004选择初始化overlap场景id=1064
2025-07-28 20:31:08,996 - INFO - 方案 #1: 初始选择后，当前总时长=2.64秒
2025-07-28 20:31:08,996 - INFO - 方案 #1: 额外添加overlap场景id=1062, 当前总时长=3.36秒
2025-07-28 20:31:08,996 - INFO - 方案 #1: 额外添加overlap场景id=1047, 当前总时长=6.08秒
2025-07-28 20:31:08,996 - INFO - 方案 #1: 额外between选择后，当前总时长=6.08秒
2025-07-28 20:31:08,996 - INFO - 方案 #1: 场景总时长(6.08秒)大于音频时长(3.98秒)，需要裁剪
2025-07-28 20:31:08,996 - INFO - 调整前总时长: 6.08秒, 目标时长: 3.98秒
2025-07-28 20:31:08,996 - INFO - 需要裁剪 2.10秒
2025-07-28 20:31:08,996 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:08,996 - INFO - 裁剪场景ID=1047：从2.72秒裁剪至1.00秒
2025-07-28 20:31:08,996 - INFO - 裁剪场景ID=1046：从1.96秒裁剪至1.58秒
2025-07-28 20:31:08,996 - INFO - 调整后总时长: 3.98秒，与目标时长差异: 0.00秒
2025-07-28 20:31:08,996 - INFO - 方案 #1 调整/填充后最终总时长: 3.98秒
2025-07-28 20:31:08,996 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:08,996 - INFO - 开始生成方案 #2
2025-07-28 20:31:08,996 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:08,996 - INFO - 方案 #2: 为字幕#994选择初始化between场景id=1049
2025-07-28 20:31:08,996 - INFO - 方案 #2: 为字幕#1004选择初始化between场景id=1065
2025-07-28 20:31:08,996 - INFO - 方案 #2: 额外between选择后，当前总时长=2.60秒
2025-07-28 20:31:08,996 - INFO - 方案 #2: 额外添加between场景id=1044, 当前总时长=3.44秒
2025-07-28 20:31:08,996 - INFO - 方案 #2: 额外添加between场景id=1045, 当前总时长=4.92秒
2025-07-28 20:31:08,996 - INFO - 方案 #2: 场景总时长(4.92秒)大于音频时长(3.98秒)，需要裁剪
2025-07-28 20:31:08,996 - INFO - 调整前总时长: 4.92秒, 目标时长: 3.98秒
2025-07-28 20:31:08,996 - INFO - 需要裁剪 0.94秒
2025-07-28 20:31:08,996 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:08,996 - INFO - 裁剪场景ID=1049：从1.88秒裁剪至1.00秒
2025-07-28 20:31:08,996 - INFO - 裁剪场景ID=1045：从1.48秒裁剪至1.42秒
2025-07-28 20:31:08,996 - INFO - 调整后总时长: 3.98秒，与目标时长差异: 0.00秒
2025-07-28 20:31:08,996 - INFO - 方案 #2 调整/填充后最终总时长: 3.98秒
2025-07-28 20:31:08,996 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:08,996 - INFO - 开始生成方案 #3
2025-07-28 20:31:08,996 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:08,996 - INFO - 方案 #3: 为字幕#994选择初始化between场景id=1048
2025-07-28 20:31:08,996 - INFO - 方案 #3: 额外between选择后，当前总时长=2.60秒
2025-07-28 20:31:08,996 - INFO - 方案 #3: 额外添加between场景id=1050, 当前总时长=4.76秒
2025-07-28 20:31:08,996 - INFO - 方案 #3: 场景总时长(4.76秒)大于音频时长(3.98秒)，需要裁剪
2025-07-28 20:31:08,996 - INFO - 调整前总时长: 4.76秒, 目标时长: 3.98秒
2025-07-28 20:31:08,996 - INFO - 需要裁剪 0.78秒
2025-07-28 20:31:08,996 - INFO - 裁剪最长场景ID=1048：从2.60秒裁剪至1.82秒
2025-07-28 20:31:08,996 - INFO - 调整后总时长: 3.98秒，与目标时长差异: 0.00秒
2025-07-28 20:31:08,996 - INFO - 方案 #3 调整/填充后最终总时长: 3.98秒
2025-07-28 20:31:08,996 - INFO - 方案 #3 添加到方案列表
2025-07-28 20:31:08,996 - INFO - 开始生成方案 #4
2025-07-28 20:31:08,996 - INFO - 方案 #4: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:08,996 - INFO - 方案 #4: 为字幕#994选择初始化between场景id=1051
2025-07-28 20:31:08,996 - INFO - 方案 #4: 额外between选择后，当前总时长=2.56秒
2025-07-28 20:31:08,996 - INFO - 方案 #4: 场景总时长(2.56秒)小于音频时长(3.98秒)，需要延伸填充
2025-07-28 20:31:08,996 - INFO - 方案 #4: 最后一个场景ID: 1051
2025-07-28 20:31:08,996 - INFO - 方案 #4: 找到最后一个场景在原始列表中的索引: 1050
2025-07-28 20:31:08,997 - INFO - 方案 #4: 需要填充时长: 1.42秒
2025-07-28 20:31:08,997 - INFO - 方案 #4: 追加场景 scene_id=1052 (裁剪至 1.42秒)
2025-07-28 20:31:08,997 - INFO - 方案 #4: 成功填充至目标时长
2025-07-28 20:31:08,997 - INFO - 方案 #4 调整/填充后最终总时长: 3.98秒
2025-07-28 20:31:08,997 - INFO - 方案 #4 添加到方案列表
2025-07-28 20:31:08,997 - INFO - 开始生成方案 #5
2025-07-28 20:31:08,997 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:08,997 - INFO - 开始生成方案 #6
2025-07-28 20:31:08,997 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:08,997 - INFO - ========== 字幕 #14 的 4 套有效场景方案生成完成 ==========
2025-07-28 20:31:08,997 - INFO - 
----- 处理字幕 #14 的方案 #1 -----
2025-07-28 20:31:08,997 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-28 20:31:08,997 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplzpu0559
2025-07-28 20:31:08,997 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1046.mp4 (确认存在: True)
2025-07-28 20:31:08,997 - INFO - 添加场景ID=1046，时长=1.96秒，累计时长=1.96秒
2025-07-28 20:31:08,997 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1064.mp4 (确认存在: True)
2025-07-28 20:31:08,998 - INFO - 添加场景ID=1064，时长=0.68秒，累计时长=2.64秒
2025-07-28 20:31:08,998 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1062.mp4 (确认存在: True)
2025-07-28 20:31:08,998 - INFO - 添加场景ID=1062，时长=0.72秒，累计时长=3.36秒
2025-07-28 20:31:08,998 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1047.mp4 (确认存在: True)
2025-07-28 20:31:08,998 - INFO - 添加场景ID=1047，时长=2.72秒，累计时长=6.08秒
2025-07-28 20:31:08,998 - INFO - 场景总时长(6.08秒)已达到音频时长(3.98秒)的1.5倍，停止添加场景
2025-07-28 20:31:08,998 - INFO - 准备合并 4 个场景文件，总时长约 6.08秒
2025-07-28 20:31:08,998 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1046.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1064.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1062.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1047.mp4'

2025-07-28 20:31:08,998 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplzpu0559\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplzpu0559\temp_combined.mp4
2025-07-28 20:31:09,175 - INFO - 合并后的视频时长: 6.17秒，目标音频时长: 3.98秒
2025-07-28 20:31:09,175 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplzpu0559\temp_combined.mp4 -ss 0 -to 3.982 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-28 20:31:09,471 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:09,471 - INFO - 目标音频时长: 3.98秒
2025-07-28 20:31:09,471 - INFO - 实际视频时长: 4.02秒
2025-07-28 20:31:09,471 - INFO - 时长差异: 0.04秒 (1.03%)
2025-07-28 20:31:09,471 - INFO - ==========================================
2025-07-28 20:31:09,471 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:09,471 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-28 20:31:09,472 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplzpu0559
2025-07-28 20:31:09,521 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:09,521 - INFO -   - 音频时长: 3.98秒
2025-07-28 20:31:09,521 - INFO -   - 视频时长: 4.02秒
2025-07-28 20:31:09,521 - INFO -   - 时长差异: 0.04秒 (1.03%)
2025-07-28 20:31:09,521 - INFO - 
----- 处理字幕 #14 的方案 #2 -----
2025-07-28 20:31:09,521 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-28 20:31:09,521 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp141yjror
2025-07-28 20:31:09,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1049.mp4 (确认存在: True)
2025-07-28 20:31:09,522 - INFO - 添加场景ID=1049，时长=1.88秒，累计时长=1.88秒
2025-07-28 20:31:09,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1065.mp4 (确认存在: True)
2025-07-28 20:31:09,522 - INFO - 添加场景ID=1065，时长=0.72秒，累计时长=2.60秒
2025-07-28 20:31:09,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1044.mp4 (确认存在: True)
2025-07-28 20:31:09,522 - INFO - 添加场景ID=1044，时长=0.84秒，累计时长=3.44秒
2025-07-28 20:31:09,522 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1045.mp4 (确认存在: True)
2025-07-28 20:31:09,522 - INFO - 添加场景ID=1045，时长=1.48秒，累计时长=4.92秒
2025-07-28 20:31:09,522 - INFO - 准备合并 4 个场景文件，总时长约 4.92秒
2025-07-28 20:31:09,522 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1049.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1065.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1044.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1045.mp4'

2025-07-28 20:31:09,523 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp141yjror\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp141yjror\temp_combined.mp4
2025-07-28 20:31:09,708 - INFO - 合并后的视频时长: 5.01秒，目标音频时长: 3.98秒
2025-07-28 20:31:09,708 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp141yjror\temp_combined.mp4 -ss 0 -to 3.982 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-28 20:31:10,020 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:10,020 - INFO - 目标音频时长: 3.98秒
2025-07-28 20:31:10,020 - INFO - 实际视频时长: 4.02秒
2025-07-28 20:31:10,020 - INFO - 时长差异: 0.04秒 (1.03%)
2025-07-28 20:31:10,020 - INFO - ==========================================
2025-07-28 20:31:10,020 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:10,020 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-28 20:31:10,021 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp141yjror
2025-07-28 20:31:10,062 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:10,062 - INFO -   - 音频时长: 3.98秒
2025-07-28 20:31:10,062 - INFO -   - 视频时长: 4.02秒
2025-07-28 20:31:10,062 - INFO -   - 时长差异: 0.04秒 (1.03%)
2025-07-28 20:31:10,062 - INFO - 
----- 处理字幕 #14 的方案 #3 -----
2025-07-28 20:31:10,062 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-28 20:31:10,062 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp43cmsptn
2025-07-28 20:31:10,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1048.mp4 (确认存在: True)
2025-07-28 20:31:10,063 - INFO - 添加场景ID=1048，时长=2.60秒，累计时长=2.60秒
2025-07-28 20:31:10,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1050.mp4 (确认存在: True)
2025-07-28 20:31:10,063 - INFO - 添加场景ID=1050，时长=2.16秒，累计时长=4.76秒
2025-07-28 20:31:10,063 - INFO - 准备合并 2 个场景文件，总时长约 4.76秒
2025-07-28 20:31:10,063 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1048.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1050.mp4'

2025-07-28 20:31:10,063 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp43cmsptn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp43cmsptn\temp_combined.mp4
2025-07-28 20:31:10,239 - INFO - 合并后的视频时长: 4.81秒，目标音频时长: 3.98秒
2025-07-28 20:31:10,239 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp43cmsptn\temp_combined.mp4 -ss 0 -to 3.982 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-28 20:31:10,577 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:10,577 - INFO - 目标音频时长: 3.98秒
2025-07-28 20:31:10,577 - INFO - 实际视频时长: 4.02秒
2025-07-28 20:31:10,577 - INFO - 时长差异: 0.04秒 (1.03%)
2025-07-28 20:31:10,577 - INFO - ==========================================
2025-07-28 20:31:10,577 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:10,577 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-28 20:31:10,578 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp43cmsptn
2025-07-28 20:31:10,634 - INFO - 方案 #3 处理完成:
2025-07-28 20:31:10,634 - INFO -   - 音频时长: 3.98秒
2025-07-28 20:31:10,634 - INFO -   - 视频时长: 4.02秒
2025-07-28 20:31:10,634 - INFO -   - 时长差异: 0.04秒 (1.03%)
2025-07-28 20:31:10,634 - INFO - 
----- 处理字幕 #14 的方案 #4 -----
2025-07-28 20:31:10,634 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_4.mp4
2025-07-28 20:31:10,634 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2oeg0xv_
2025-07-28 20:31:10,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1051.mp4 (确认存在: True)
2025-07-28 20:31:10,635 - INFO - 添加场景ID=1051，时长=2.56秒，累计时长=2.56秒
2025-07-28 20:31:10,635 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1052.mp4 (确认存在: True)
2025-07-28 20:31:10,635 - INFO - 添加场景ID=1052，时长=3.28秒，累计时长=5.84秒
2025-07-28 20:31:10,635 - INFO - 准备合并 2 个场景文件，总时长约 5.84秒
2025-07-28 20:31:10,635 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1051.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1052.mp4'

2025-07-28 20:31:10,635 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2oeg0xv_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2oeg0xv_\temp_combined.mp4
2025-07-28 20:31:10,770 - INFO - 合并后的视频时长: 5.89秒，目标音频时长: 3.98秒
2025-07-28 20:31:10,770 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2oeg0xv_\temp_combined.mp4 -ss 0 -to 3.982 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_4.mp4
2025-07-28 20:31:11,062 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:11,062 - INFO - 目标音频时长: 3.98秒
2025-07-28 20:31:11,062 - INFO - 实际视频时长: 4.02秒
2025-07-28 20:31:11,062 - INFO - 时长差异: 0.04秒 (1.03%)
2025-07-28 20:31:11,062 - INFO - ==========================================
2025-07-28 20:31:11,062 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:11,062 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_4.mp4
2025-07-28 20:31:11,064 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2oeg0xv_
2025-07-28 20:31:11,104 - INFO - 方案 #4 处理完成:
2025-07-28 20:31:11,104 - INFO -   - 音频时长: 3.98秒
2025-07-28 20:31:11,104 - INFO -   - 视频时长: 4.02秒
2025-07-28 20:31:11,104 - INFO -   - 时长差异: 0.04秒 (1.03%)
2025-07-28 20:31:11,104 - INFO - 
字幕 #14 处理完成，成功生成 4/4 套方案
2025-07-28 20:31:11,104 - INFO - 生成的视频文件:
2025-07-28 20:31:11,104 - INFO -   1. F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-07-28 20:31:11,104 - INFO -   2. F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-07-28 20:31:11,104 - INFO -   3. F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-07-28 20:31:11,104 - INFO -   4. F:/github/aicut_auto/newcut_ai\14_4.mp4
2025-07-28 20:31:11,104 - INFO - ========== 字幕 #14 处理结束 ==========

