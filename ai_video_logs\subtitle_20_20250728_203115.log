2025-07-28 20:31:15,242 - INFO - ========== 字幕 #20 处理开始 ==========
2025-07-28 20:31:15,242 - INFO - 字幕内容: 他甚至认为女孩是想借机怂恿大家，为她父亲报仇。
2025-07-28 20:31:15,242 - INFO - 字幕序号: [1813, 1816]
2025-07-28 20:31:15,243 - INFO - 音频文件详情:
2025-07-28 20:31:15,243 - INFO -   - 路径: output\20.wav
2025-07-28 20:31:15,243 - INFO -   - 时长: 1.82秒
2025-07-28 20:31:15,243 - INFO -   - 验证音频时长: 1.82秒
2025-07-28 20:31:15,243 - INFO - 字幕时间戳信息:
2025-07-28 20:31:15,243 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:15,243 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:15,243 - INFO -   - 根据生成的音频时长(1.82秒)已调整字幕时间戳
2025-07-28 20:31:15,243 - INFO - ========== 开始为字幕 #20 生成 6 套场景方案 ==========
2025-07-28 20:31:15,243 - INFO - 开始查找字幕序号 [1813, 1816] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:15,244 - INFO - 找到related_overlap场景: scene_id=1867, 字幕#1813
2025-07-28 20:31:15,244 - INFO - 找到related_overlap场景: scene_id=1869, 字幕#1816
2025-07-28 20:31:15,244 - INFO - 字幕 #1813 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:15,244 - INFO - 字幕 #1816 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:15,244 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:15,244 - INFO - 开始生成方案 #1
2025-07-28 20:31:15,244 - INFO - 方案 #1: 为字幕#1813选择初始化overlap场景id=1867
2025-07-28 20:31:15,244 - INFO - 方案 #1: 为字幕#1816选择初始化overlap场景id=1869
2025-07-28 20:31:15,244 - INFO - 方案 #1: 初始选择后，当前总时长=2.92秒
2025-07-28 20:31:15,244 - INFO - 方案 #1: 额外between选择后，当前总时长=2.92秒
2025-07-28 20:31:15,244 - INFO - 方案 #1: 场景总时长(2.92秒)大于音频时长(1.82秒)，需要裁剪
2025-07-28 20:31:15,244 - INFO - 调整前总时长: 2.92秒, 目标时长: 1.82秒
2025-07-28 20:31:15,244 - INFO - 需要裁剪 1.10秒
2025-07-28 20:31:15,244 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:15,244 - INFO - 裁剪场景ID=1867：从1.88秒裁剪至1.00秒
2025-07-28 20:31:15,244 - INFO - 裁剪场景ID=1869：从1.04秒裁剪至1.00秒
2025-07-28 20:31:15,244 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.18秒
2025-07-28 20:31:15,244 - INFO - 调整后总时长: 2.00秒，与目标时长差异: 0.18秒
2025-07-28 20:31:15,244 - INFO - 方案 #1 调整/填充后最终总时长: 2.00秒
2025-07-28 20:31:15,244 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:15,244 - INFO - 开始生成方案 #2
2025-07-28 20:31:15,244 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:15,244 - INFO - 开始生成方案 #3
2025-07-28 20:31:15,244 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:15,244 - INFO - 开始生成方案 #4
2025-07-28 20:31:15,244 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:15,244 - INFO - 开始生成方案 #5
2025-07-28 20:31:15,244 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:15,244 - INFO - 开始生成方案 #6
2025-07-28 20:31:15,244 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:15,244 - INFO - ========== 字幕 #20 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:15,246 - INFO - 
----- 处理字幕 #20 的方案 #1 -----
2025-07-28 20:31:15,246 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-28 20:31:15,246 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7oy2vysg
2025-07-28 20:31:15,246 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1867.mp4 (确认存在: True)
2025-07-28 20:31:15,246 - INFO - 添加场景ID=1867，时长=1.88秒，累计时长=1.88秒
2025-07-28 20:31:15,247 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1869.mp4 (确认存在: True)
2025-07-28 20:31:15,247 - INFO - 添加场景ID=1869，时长=1.04秒，累计时长=2.92秒
2025-07-28 20:31:15,247 - INFO - 场景总时长(2.92秒)已达到音频时长(1.82秒)的1.5倍，停止添加场景
2025-07-28 20:31:15,247 - INFO - 准备合并 2 个场景文件，总时长约 2.92秒
2025-07-28 20:31:15,247 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1867.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1869.mp4'

2025-07-28 20:31:15,247 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7oy2vysg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7oy2vysg\temp_combined.mp4
2025-07-28 20:31:15,366 - INFO - 合并后的视频时长: 2.97秒，目标音频时长: 1.82秒
2025-07-28 20:31:15,366 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7oy2vysg\temp_combined.mp4 -ss 0 -to 1.823 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-28 20:31:15,564 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:15,564 - INFO - 目标音频时长: 1.82秒
2025-07-28 20:31:15,564 - INFO - 实际视频时长: 1.86秒
2025-07-28 20:31:15,564 - INFO - 时长差异: 0.04秒 (2.19%)
2025-07-28 20:31:15,564 - INFO - ==========================================
2025-07-28 20:31:15,564 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:15,564 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-28 20:31:15,564 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7oy2vysg
2025-07-28 20:31:15,605 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:15,605 - INFO -   - 音频时长: 1.82秒
2025-07-28 20:31:15,605 - INFO -   - 视频时长: 1.86秒
2025-07-28 20:31:15,605 - INFO -   - 时长差异: 0.04秒 (2.19%)
2025-07-28 20:31:15,605 - INFO - 
字幕 #20 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:15,605 - INFO - 生成的视频文件:
2025-07-28 20:31:15,605 - INFO -   1. F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-07-28 20:31:15,605 - INFO - ========== 字幕 #20 处理结束 ==========

