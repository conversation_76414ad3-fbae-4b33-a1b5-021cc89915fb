2025-07-28 20:31:26,402 - INFO - ========== 字幕 #32 处理开始 ==========
2025-07-28 20:31:26,402 - INFO - 字幕内容: 被救的男人不敢相信救自己的竟是个小姑娘，他正是微服私访的当朝太子。
2025-07-28 20:31:26,402 - INFO - 字幕序号: [2264, 2267]
2025-07-28 20:31:26,402 - INFO - 音频文件详情:
2025-07-28 20:31:26,402 - INFO -   - 路径: output\32.wav
2025-07-28 20:31:26,402 - INFO -   - 时长: 3.90秒
2025-07-28 20:31:26,403 - INFO -   - 验证音频时长: 3.90秒
2025-07-28 20:31:26,403 - INFO - 字幕时间戳信息:
2025-07-28 20:31:26,403 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:26,403 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:26,403 - INFO -   - 根据生成的音频时长(3.90秒)已调整字幕时间戳
2025-07-28 20:31:26,403 - INFO - ========== 开始为字幕 #32 生成 6 套场景方案 ==========
2025-07-28 20:31:26,403 - INFO - 开始查找字幕序号 [2264, 2267] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:26,404 - INFO - 找到related_overlap场景: scene_id=2360, 字幕#2264
2025-07-28 20:31:26,404 - INFO - 找到related_overlap场景: scene_id=2365, 字幕#2267
2025-07-28 20:31:26,404 - INFO - 找到related_between场景: scene_id=2364, 字幕#2267
2025-07-28 20:31:26,404 - INFO - 字幕 #2264 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:26,404 - INFO - 字幕 #2267 找到 1 个overlap场景, 1 个between场景
2025-07-28 20:31:26,404 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 20:31:26,404 - INFO - 开始生成方案 #1
2025-07-28 20:31:26,405 - INFO - 方案 #1: 为字幕#2264选择初始化overlap场景id=2360
2025-07-28 20:31:26,405 - INFO - 方案 #1: 为字幕#2267选择初始化overlap场景id=2365
2025-07-28 20:31:26,405 - INFO - 方案 #1: 初始选择后，当前总时长=2.76秒
2025-07-28 20:31:26,405 - INFO - 方案 #1: 额外between选择后，当前总时长=2.76秒
2025-07-28 20:31:26,405 - INFO - 方案 #1: 额外添加between场景id=2364, 当前总时长=3.76秒
2025-07-28 20:31:26,405 - INFO - 方案 #1: 场景总时长(3.76秒)小于音频时长(3.90秒)，需要延伸填充
2025-07-28 20:31:26,405 - INFO - 方案 #1: 最后一个场景ID: 2364
2025-07-28 20:31:26,405 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2363
2025-07-28 20:31:26,405 - INFO - 方案 #1: 需要填充时长: 0.14秒
2025-07-28 20:31:26,405 - INFO - 方案 #1: 跳过已使用的场景: scene_id=2365
2025-07-28 20:31:26,405 - INFO - 方案 #1: 追加场景 scene_id=2366 (裁剪至 0.14秒)
2025-07-28 20:31:26,405 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 20:31:26,405 - INFO - 方案 #1 调整/填充后最终总时长: 3.90秒
2025-07-28 20:31:26,405 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:26,405 - INFO - 开始生成方案 #2
2025-07-28 20:31:26,405 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,405 - INFO - 开始生成方案 #3
2025-07-28 20:31:26,405 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,405 - INFO - 开始生成方案 #4
2025-07-28 20:31:26,405 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,405 - INFO - 开始生成方案 #5
2025-07-28 20:31:26,405 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,405 - INFO - 开始生成方案 #6
2025-07-28 20:31:26,405 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,405 - INFO - ========== 字幕 #32 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:26,405 - INFO - 
----- 处理字幕 #32 的方案 #1 -----
2025-07-28 20:31:26,405 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-28 20:31:26,405 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe7uqfpb5
2025-07-28 20:31:26,406 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2360.mp4 (确认存在: True)
2025-07-28 20:31:26,406 - INFO - 添加场景ID=2360，时长=1.28秒，累计时长=1.28秒
2025-07-28 20:31:26,406 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2365.mp4 (确认存在: True)
2025-07-28 20:31:26,406 - INFO - 添加场景ID=2365，时长=1.48秒，累计时长=2.76秒
2025-07-28 20:31:26,406 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2364.mp4 (确认存在: True)
2025-07-28 20:31:26,406 - INFO - 添加场景ID=2364，时长=1.00秒，累计时长=3.76秒
2025-07-28 20:31:26,406 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2366.mp4 (确认存在: True)
2025-07-28 20:31:26,406 - INFO - 添加场景ID=2366，时长=2.84秒，累计时长=6.60秒
2025-07-28 20:31:26,406 - INFO - 场景总时长(6.60秒)已达到音频时长(3.90秒)的1.5倍，停止添加场景
2025-07-28 20:31:26,406 - INFO - 准备合并 4 个场景文件，总时长约 6.60秒
2025-07-28 20:31:26,406 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2360.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2365.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2364.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2366.mp4'

2025-07-28 20:31:26,406 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe7uqfpb5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe7uqfpb5\temp_combined.mp4
2025-07-28 20:31:26,577 - INFO - 合并后的视频时长: 6.69秒，目标音频时长: 3.90秒
2025-07-28 20:31:26,577 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe7uqfpb5\temp_combined.mp4 -ss 0 -to 3.902 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-28 20:31:26,862 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:26,862 - INFO - 目标音频时长: 3.90秒
2025-07-28 20:31:26,862 - INFO - 实际视频时长: 3.94秒
2025-07-28 20:31:26,862 - INFO - 时长差异: 0.04秒 (1.05%)
2025-07-28 20:31:26,862 - INFO - ==========================================
2025-07-28 20:31:26,862 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:26,862 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-28 20:31:26,863 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe7uqfpb5
2025-07-28 20:31:26,904 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:26,904 - INFO -   - 音频时长: 3.90秒
2025-07-28 20:31:26,904 - INFO -   - 视频时长: 3.94秒
2025-07-28 20:31:26,904 - INFO -   - 时长差异: 0.04秒 (1.05%)
2025-07-28 20:31:26,904 - INFO - 
字幕 #32 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:26,904 - INFO - 生成的视频文件:
2025-07-28 20:31:26,904 - INFO -   1. F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-07-28 20:31:26,904 - INFO - ========== 字幕 #32 处理结束 ==========

