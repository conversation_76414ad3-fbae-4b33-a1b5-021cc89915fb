2025-07-28 20:30:58,315 - INFO - ========== 字幕 #1 处理开始 ==========
2025-07-28 20:30:58,316 - INFO - 字幕内容: 男人为给女儿补身体，上山打猎却被野猪重伤，生死一线。
2025-07-28 20:30:58,316 - INFO - 字幕序号: [19, 27]
2025-07-28 20:30:58,316 - INFO - 音频文件详情:
2025-07-28 20:30:58,316 - INFO -   - 路径: output\1.wav
2025-07-28 20:30:58,316 - INFO -   - 时长: 4.00秒
2025-07-28 20:30:58,316 - INFO -   - 验证音频时长: 4.00秒
2025-07-28 20:30:58,316 - INFO - 字幕时间戳信息:
2025-07-28 20:30:58,316 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:30:58,316 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:30:58,316 - INFO -   - 根据生成的音频时长(4.00秒)已调整字幕时间戳
2025-07-28 20:30:58,316 - INFO - ========== 开始为字幕 #1 生成 6 套场景方案 ==========
2025-07-28 20:30:58,316 - INFO - 开始查找字幕序号 [19, 27] 对应的场景，共有 3562 个场景可选
2025-07-28 20:30:58,316 - INFO - 找到related_overlap场景: scene_id=19, 字幕#19
2025-07-28 20:30:58,316 - INFO - 找到related_overlap场景: scene_id=20, 字幕#19
2025-07-28 20:30:58,316 - INFO - 找到related_overlap场景: scene_id=25, 字幕#27
2025-07-28 20:30:58,316 - INFO - 找到related_overlap场景: scene_id=26, 字幕#27
2025-07-28 20:30:58,318 - INFO - 字幕 #19 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:30:58,318 - INFO - 字幕 #27 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:30:58,318 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:30:58,318 - INFO - 开始生成方案 #1
2025-07-28 20:30:58,318 - INFO - 方案 #1: 为字幕#19选择初始化overlap场景id=19
2025-07-28 20:30:58,318 - INFO - 方案 #1: 为字幕#27选择初始化overlap场景id=26
2025-07-28 20:30:58,318 - INFO - 方案 #1: 初始选择后，当前总时长=2.16秒
2025-07-28 20:30:58,318 - INFO - 方案 #1: 额外添加overlap场景id=25, 当前总时长=3.20秒
2025-07-28 20:30:58,318 - INFO - 方案 #1: 额外添加overlap场景id=20, 当前总时长=5.16秒
2025-07-28 20:30:58,318 - INFO - 方案 #1: 额外between选择后，当前总时长=5.16秒
2025-07-28 20:30:58,318 - INFO - 方案 #1: 场景总时长(5.16秒)大于音频时长(4.00秒)，需要裁剪
2025-07-28 20:30:58,318 - INFO - 调整前总时长: 5.16秒, 目标时长: 4.00秒
2025-07-28 20:30:58,318 - INFO - 需要裁剪 1.16秒
2025-07-28 20:30:58,318 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:30:58,318 - INFO - 裁剪场景ID=20：从1.96秒裁剪至1.00秒
2025-07-28 20:30:58,318 - INFO - 裁剪场景ID=26：从1.20秒裁剪至1.00秒
2025-07-28 20:30:58,318 - INFO - 调整后总时长: 4.00秒，与目标时长差异: 0.00秒
2025-07-28 20:30:58,318 - INFO - 方案 #1 调整/填充后最终总时长: 4.00秒
2025-07-28 20:30:58,318 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:30:58,318 - INFO - 开始生成方案 #2
2025-07-28 20:30:58,318 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:58,318 - INFO - 开始生成方案 #3
2025-07-28 20:30:58,318 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:58,318 - INFO - 开始生成方案 #4
2025-07-28 20:30:58,318 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:58,318 - INFO - 开始生成方案 #5
2025-07-28 20:30:58,318 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:58,318 - INFO - 开始生成方案 #6
2025-07-28 20:30:58,318 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:58,318 - INFO - ========== 字幕 #1 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:30:58,318 - INFO - 
----- 处理字幕 #1 的方案 #1 -----
2025-07-28 20:30:58,318 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-28 20:30:58,319 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptugu25q0
2025-07-28 20:30:58,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\19.mp4 (确认存在: True)
2025-07-28 20:30:58,320 - INFO - 添加场景ID=19，时长=0.96秒，累计时长=0.96秒
2025-07-28 20:30:58,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\26.mp4 (确认存在: True)
2025-07-28 20:30:58,320 - INFO - 添加场景ID=26，时长=1.20秒，累计时长=2.16秒
2025-07-28 20:30:58,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\25.mp4 (确认存在: True)
2025-07-28 20:30:58,320 - INFO - 添加场景ID=25，时长=1.04秒，累计时长=3.20秒
2025-07-28 20:30:58,320 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\20.mp4 (确认存在: True)
2025-07-28 20:30:58,320 - INFO - 添加场景ID=20，时长=1.96秒，累计时长=5.16秒
2025-07-28 20:30:58,320 - INFO - 准备合并 4 个场景文件，总时长约 5.16秒
2025-07-28 20:30:58,320 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/19.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/26.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/25.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/20.mp4'

2025-07-28 20:30:58,320 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptugu25q0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptugu25q0\temp_combined.mp4
2025-07-28 20:30:58,742 - INFO - 合并后的视频时长: 5.25秒，目标音频时长: 4.00秒
2025-07-28 20:30:58,742 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptugu25q0\temp_combined.mp4 -ss 0 -to 4.004 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-28 20:30:59,034 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:30:59,035 - INFO - 目标音频时长: 4.00秒
2025-07-28 20:30:59,035 - INFO - 实际视频时长: 4.06秒
2025-07-28 20:30:59,035 - INFO - 时长差异: 0.06秒 (1.47%)
2025-07-28 20:30:59,035 - INFO - ==========================================
2025-07-28 20:30:59,035 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:30:59,035 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-28 20:30:59,035 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptugu25q0
2025-07-28 20:30:59,075 - INFO - 方案 #1 处理完成:
2025-07-28 20:30:59,075 - INFO -   - 音频时长: 4.00秒
2025-07-28 20:30:59,075 - INFO -   - 视频时长: 4.06秒
2025-07-28 20:30:59,075 - INFO -   - 时长差异: 0.06秒 (1.47%)
2025-07-28 20:30:59,075 - INFO - 
字幕 #1 处理完成，成功生成 1/1 套方案
2025-07-28 20:30:59,075 - INFO - 生成的视频文件:
2025-07-28 20:30:59,075 - INFO -   1. F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-07-28 20:30:59,075 - INFO - ========== 字幕 #1 处理结束 ==========

