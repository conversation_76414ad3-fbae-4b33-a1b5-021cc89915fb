2025-07-28 20:31:05,577 - INFO - ========== 字幕 #9 处理开始 ==========
2025-07-28 20:31:05,577 - INFO - 字幕内容: 完成任务的女孩，获得了系统的神级箭术奖励，命运的齿轮开始转动。
2025-07-28 20:31:05,577 - INFO - 字幕序号: [215, 216]
2025-07-28 20:31:05,577 - INFO - 音频文件详情:
2025-07-28 20:31:05,577 - INFO -   - 路径: output\9.wav
2025-07-28 20:31:05,577 - INFO -   - 时长: 3.62秒
2025-07-28 20:31:05,577 - INFO -   - 验证音频时长: 3.62秒
2025-07-28 20:31:05,577 - INFO - 字幕时间戳信息:
2025-07-28 20:31:05,577 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:05,577 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:05,577 - INFO -   - 根据生成的音频时长(3.62秒)已调整字幕时间戳
2025-07-28 20:31:05,577 - INFO - ========== 开始为字幕 #9 生成 6 套场景方案 ==========
2025-07-28 20:31:05,577 - INFO - 开始查找字幕序号 [215, 216] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:05,578 - INFO - 找到related_overlap场景: scene_id=259, 字幕#215
2025-07-28 20:31:05,578 - INFO - 找到related_overlap场景: scene_id=260, 字幕#215
2025-07-28 20:31:05,578 - INFO - 找到related_between场景: scene_id=258, 字幕#215
2025-07-28 20:31:05,579 - INFO - 字幕 #215 找到 2 个overlap场景, 1 个between场景
2025-07-28 20:31:05,579 - INFO - 字幕 #216 找到 0 个overlap场景, 0 个between场景
2025-07-28 20:31:05,579 - WARNING - 字幕 #216 没有找到任何匹配场景!
2025-07-28 20:31:05,579 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 20:31:05,579 - INFO - 开始生成方案 #1
2025-07-28 20:31:05,579 - INFO - 方案 #1: 为字幕#215选择初始化overlap场景id=259
2025-07-28 20:31:05,579 - INFO - 方案 #1: 初始选择后，当前总时长=3.04秒
2025-07-28 20:31:05,579 - INFO - 方案 #1: 额外添加overlap场景id=260, 当前总时长=5.76秒
2025-07-28 20:31:05,579 - INFO - 方案 #1: 额外between选择后，当前总时长=5.76秒
2025-07-28 20:31:05,579 - INFO - 方案 #1: 场景总时长(5.76秒)大于音频时长(3.62秒)，需要裁剪
2025-07-28 20:31:05,579 - INFO - 调整前总时长: 5.76秒, 目标时长: 3.62秒
2025-07-28 20:31:05,579 - INFO - 需要裁剪 2.14秒
2025-07-28 20:31:05,579 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:05,579 - INFO - 裁剪场景ID=259：从3.04秒裁剪至1.00秒
2025-07-28 20:31:05,579 - INFO - 裁剪场景ID=260：从2.72秒裁剪至2.62秒
2025-07-28 20:31:05,579 - INFO - 调整后总时长: 3.62秒，与目标时长差异: 0.00秒
2025-07-28 20:31:05,579 - INFO - 方案 #1 调整/填充后最终总时长: 3.62秒
2025-07-28 20:31:05,579 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:05,579 - INFO - 开始生成方案 #2
2025-07-28 20:31:05,579 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:05,579 - INFO - 方案 #2: 为字幕#215选择初始化between场景id=258
2025-07-28 20:31:05,579 - INFO - 方案 #2: 额外between选择后，当前总时长=1.24秒
2025-07-28 20:31:05,579 - INFO - 方案 #2: 场景总时长(1.24秒)小于音频时长(3.62秒)，需要延伸填充
2025-07-28 20:31:05,579 - INFO - 方案 #2: 最后一个场景ID: 258
2025-07-28 20:31:05,579 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 257
2025-07-28 20:31:05,579 - INFO - 方案 #2: 需要填充时长: 2.38秒
2025-07-28 20:31:05,579 - INFO - 方案 #2: 跳过已使用的场景: scene_id=259
2025-07-28 20:31:05,579 - INFO - 方案 #2: 跳过已使用的场景: scene_id=260
2025-07-28 20:31:05,579 - INFO - 方案 #2: 追加场景 scene_id=261 (裁剪至 2.38秒)
2025-07-28 20:31:05,579 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:05,579 - INFO - 方案 #2 调整/填充后最终总时长: 3.62秒
2025-07-28 20:31:05,579 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:05,579 - INFO - 开始生成方案 #3
2025-07-28 20:31:05,579 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:05,579 - INFO - 开始生成方案 #4
2025-07-28 20:31:05,579 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:05,579 - INFO - 开始生成方案 #5
2025-07-28 20:31:05,580 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:05,580 - INFO - 开始生成方案 #6
2025-07-28 20:31:05,580 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:05,580 - INFO - ========== 字幕 #9 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:05,580 - INFO - 
----- 处理字幕 #9 的方案 #1 -----
2025-07-28 20:31:05,580 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-28 20:31:05,580 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0typtd5t
2025-07-28 20:31:05,580 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\259.mp4 (确认存在: True)
2025-07-28 20:31:05,580 - INFO - 添加场景ID=259，时长=3.04秒，累计时长=3.04秒
2025-07-28 20:31:05,580 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\260.mp4 (确认存在: True)
2025-07-28 20:31:05,581 - INFO - 添加场景ID=260，时长=2.72秒，累计时长=5.76秒
2025-07-28 20:31:05,581 - INFO - 场景总时长(5.76秒)已达到音频时长(3.62秒)的1.5倍，停止添加场景
2025-07-28 20:31:05,581 - INFO - 准备合并 2 个场景文件，总时长约 5.76秒
2025-07-28 20:31:05,581 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/259.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/260.mp4'

2025-07-28 20:31:05,581 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0typtd5t\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0typtd5t\temp_combined.mp4
2025-07-28 20:31:05,717 - INFO - 合并后的视频时长: 5.81秒，目标音频时长: 3.62秒
2025-07-28 20:31:05,717 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0typtd5t\temp_combined.mp4 -ss 0 -to 3.62 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-28 20:31:05,987 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:05,987 - INFO - 目标音频时长: 3.62秒
2025-07-28 20:31:05,987 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:05,987 - INFO - 时长差异: 0.04秒 (1.19%)
2025-07-28 20:31:05,987 - INFO - ==========================================
2025-07-28 20:31:05,987 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:05,987 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-28 20:31:05,988 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0typtd5t
2025-07-28 20:31:06,028 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:06,028 - INFO -   - 音频时长: 3.62秒
2025-07-28 20:31:06,028 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:06,028 - INFO -   - 时长差异: 0.04秒 (1.19%)
2025-07-28 20:31:06,028 - INFO - 
----- 处理字幕 #9 的方案 #2 -----
2025-07-28 20:31:06,028 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-28 20:31:06,028 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4ddn_s8g
2025-07-28 20:31:06,029 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\258.mp4 (确认存在: True)
2025-07-28 20:31:06,029 - INFO - 添加场景ID=258，时长=1.24秒，累计时长=1.24秒
2025-07-28 20:31:06,029 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\261.mp4 (确认存在: True)
2025-07-28 20:31:06,029 - INFO - 添加场景ID=261，时长=4.04秒，累计时长=5.28秒
2025-07-28 20:31:06,029 - INFO - 准备合并 2 个场景文件，总时长约 5.28秒
2025-07-28 20:31:06,029 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/258.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/261.mp4'

2025-07-28 20:31:06,029 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp4ddn_s8g\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp4ddn_s8g\temp_combined.mp4
2025-07-28 20:31:06,163 - INFO - 合并后的视频时长: 5.33秒，目标音频时长: 3.62秒
2025-07-28 20:31:06,163 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp4ddn_s8g\temp_combined.mp4 -ss 0 -to 3.62 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-28 20:31:06,420 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:06,420 - INFO - 目标音频时长: 3.62秒
2025-07-28 20:31:06,420 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:06,420 - INFO - 时长差异: 0.04秒 (1.19%)
2025-07-28 20:31:06,420 - INFO - ==========================================
2025-07-28 20:31:06,420 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:06,420 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-28 20:31:06,421 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4ddn_s8g
2025-07-28 20:31:06,462 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:06,462 - INFO -   - 音频时长: 3.62秒
2025-07-28 20:31:06,462 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:06,462 - INFO -   - 时长差异: 0.04秒 (1.19%)
2025-07-28 20:31:06,462 - INFO - 
字幕 #9 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:06,462 - INFO - 生成的视频文件:
2025-07-28 20:31:06,462 - INFO -   1. F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-07-28 20:31:06,462 - INFO -   2. F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-07-28 20:31:06,462 - INFO - ========== 字幕 #9 处理结束 ==========

