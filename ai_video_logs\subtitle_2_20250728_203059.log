2025-07-28 20:30:59,075 - INFO - ========== 字幕 #2 处理开始 ==========
2025-07-28 20:30:59,075 - INFO - 字幕内容: 天价的三十两药费，让这个本就贫寒的家庭雪上加霜。
2025-07-28 20:30:59,076 - INFO - 字幕序号: [50, 51]
2025-07-28 20:30:59,076 - INFO - 音频文件详情:
2025-07-28 20:30:59,076 - INFO -   - 路径: output\2.wav
2025-07-28 20:30:59,076 - INFO -   - 时长: 2.94秒
2025-07-28 20:30:59,076 - INFO -   - 验证音频时长: 2.94秒
2025-07-28 20:30:59,076 - INFO - 字幕时间戳信息:
2025-07-28 20:30:59,076 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:30:59,076 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:30:59,076 - INFO -   - 根据生成的音频时长(2.94秒)已调整字幕时间戳
2025-07-28 20:30:59,076 - INFO - ========== 开始为字幕 #2 生成 6 套场景方案 ==========
2025-07-28 20:30:59,076 - INFO - 开始查找字幕序号 [50, 51] 对应的场景，共有 3562 个场景可选
2025-07-28 20:30:59,076 - INFO - 找到related_overlap场景: scene_id=52, 字幕#50
2025-07-28 20:30:59,076 - INFO - 找到related_overlap场景: scene_id=53, 字幕#50
2025-07-28 20:30:59,076 - INFO - 找到related_overlap场景: scene_id=54, 字幕#51
2025-07-28 20:30:59,078 - INFO - 字幕 #50 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:30:59,078 - INFO - 字幕 #51 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:30:59,078 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:30:59,078 - INFO - 开始生成方案 #1
2025-07-28 20:30:59,078 - INFO - 方案 #1: 为字幕#50选择初始化overlap场景id=53
2025-07-28 20:30:59,078 - INFO - 方案 #1: 为字幕#51选择初始化overlap场景id=54
2025-07-28 20:30:59,078 - INFO - 方案 #1: 初始选择后，当前总时长=2.40秒
2025-07-28 20:30:59,078 - INFO - 方案 #1: 额外添加overlap场景id=52, 当前总时长=4.76秒
2025-07-28 20:30:59,078 - INFO - 方案 #1: 额外between选择后，当前总时长=4.76秒
2025-07-28 20:30:59,078 - INFO - 方案 #1: 场景总时长(4.76秒)大于音频时长(2.94秒)，需要裁剪
2025-07-28 20:30:59,078 - INFO - 调整前总时长: 4.76秒, 目标时长: 2.94秒
2025-07-28 20:30:59,078 - INFO - 需要裁剪 1.82秒
2025-07-28 20:30:59,078 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:30:59,078 - INFO - 裁剪场景ID=52：从2.36秒裁剪至1.00秒
2025-07-28 20:30:59,078 - INFO - 裁剪场景ID=53：从1.28秒裁剪至1.00秒
2025-07-28 20:30:59,078 - INFO - 裁剪场景ID=54：从1.12秒裁剪至1.00秒
2025-07-28 20:30:59,078 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.06秒
2025-07-28 20:30:59,078 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.06秒
2025-07-28 20:30:59,078 - INFO - 方案 #1 调整/填充后最终总时长: 3.00秒
2025-07-28 20:30:59,078 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:30:59,078 - INFO - 开始生成方案 #2
2025-07-28 20:30:59,078 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,078 - INFO - 开始生成方案 #3
2025-07-28 20:30:59,078 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,078 - INFO - 开始生成方案 #4
2025-07-28 20:30:59,078 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,078 - INFO - 开始生成方案 #5
2025-07-28 20:30:59,078 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,078 - INFO - 开始生成方案 #6
2025-07-28 20:30:59,078 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,078 - INFO - ========== 字幕 #2 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:30:59,078 - INFO - 
----- 处理字幕 #2 的方案 #1 -----
2025-07-28 20:30:59,078 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-28 20:30:59,079 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphk0s5w8i
2025-07-28 20:30:59,079 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\53.mp4 (确认存在: True)
2025-07-28 20:30:59,079 - INFO - 添加场景ID=53，时长=1.28秒，累计时长=1.28秒
2025-07-28 20:30:59,079 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\54.mp4 (确认存在: True)
2025-07-28 20:30:59,079 - INFO - 添加场景ID=54，时长=1.12秒，累计时长=2.40秒
2025-07-28 20:30:59,079 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\52.mp4 (确认存在: True)
2025-07-28 20:30:59,079 - INFO - 添加场景ID=52，时长=2.36秒，累计时长=4.76秒
2025-07-28 20:30:59,079 - INFO - 场景总时长(4.76秒)已达到音频时长(2.94秒)的1.5倍，停止添加场景
2025-07-28 20:30:59,079 - INFO - 准备合并 3 个场景文件，总时长约 4.76秒
2025-07-28 20:30:59,079 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/53.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/54.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/52.mp4'

2025-07-28 20:30:59,079 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphk0s5w8i\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphk0s5w8i\temp_combined.mp4
2025-07-28 20:30:59,241 - INFO - 合并后的视频时长: 4.83秒，目标音频时长: 2.94秒
2025-07-28 20:30:59,241 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphk0s5w8i\temp_combined.mp4 -ss 0 -to 2.937 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-28 20:30:59,506 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:30:59,506 - INFO - 目标音频时长: 2.94秒
2025-07-28 20:30:59,506 - INFO - 实际视频时长: 2.98秒
2025-07-28 20:30:59,506 - INFO - 时长差异: 0.05秒 (1.57%)
2025-07-28 20:30:59,506 - INFO - ==========================================
2025-07-28 20:30:59,506 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:30:59,506 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-28 20:30:59,507 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphk0s5w8i
2025-07-28 20:30:59,552 - INFO - 方案 #1 处理完成:
2025-07-28 20:30:59,552 - INFO -   - 音频时长: 2.94秒
2025-07-28 20:30:59,552 - INFO -   - 视频时长: 2.98秒
2025-07-28 20:30:59,553 - INFO -   - 时长差异: 0.05秒 (1.57%)
2025-07-28 20:30:59,553 - INFO - 
字幕 #2 处理完成，成功生成 1/1 套方案
2025-07-28 20:30:59,553 - INFO - 生成的视频文件:
2025-07-28 20:30:59,553 - INFO -   1. F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-07-28 20:30:59,553 - INFO - ========== 字幕 #2 处理结束 ==========

