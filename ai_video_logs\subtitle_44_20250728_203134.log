2025-07-28 20:31:34,829 - INFO - ========== 字幕 #44 处理开始 ==========
2025-07-28 20:31:34,829 - INFO - 字幕内容: 全村人激动地跪谢太子，太子却说，他们最该感谢的是这位不凡的女孩。
2025-07-28 20:31:34,829 - INFO - 字幕序号: [3309, 3313]
2025-07-28 20:31:34,829 - INFO - 音频文件详情:
2025-07-28 20:31:34,829 - INFO -   - 路径: output\44.wav
2025-07-28 20:31:34,829 - INFO -   - 时长: 4.43秒
2025-07-28 20:31:34,830 - INFO -   - 验证音频时长: 4.43秒
2025-07-28 20:31:34,830 - INFO - 字幕时间戳信息:
2025-07-28 20:31:34,838 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:34,838 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:34,838 - INFO -   - 根据生成的音频时长(4.43秒)已调整字幕时间戳
2025-07-28 20:31:34,839 - INFO - ========== 开始为字幕 #44 生成 6 套场景方案 ==========
2025-07-28 20:31:34,839 - INFO - 开始查找字幕序号 [3309, 3313] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:34,839 - INFO - 找到related_overlap场景: scene_id=3477, 字幕#3309
2025-07-28 20:31:34,839 - INFO - 找到related_overlap场景: scene_id=3478, 字幕#3309
2025-07-28 20:31:34,839 - INFO - 找到related_overlap场景: scene_id=3483, 字幕#3313
2025-07-28 20:31:34,840 - INFO - 找到related_between场景: scene_id=3476, 字幕#3309
2025-07-28 20:31:34,840 - INFO - 找到related_between场景: scene_id=3484, 字幕#3313
2025-07-28 20:31:34,840 - INFO - 找到related_between场景: scene_id=3485, 字幕#3313
2025-07-28 20:31:34,840 - INFO - 找到related_between场景: scene_id=3486, 字幕#3313
2025-07-28 20:31:34,840 - INFO - 字幕 #3309 找到 2 个overlap场景, 1 个between场景
2025-07-28 20:31:34,840 - INFO - 字幕 #3313 找到 1 个overlap场景, 3 个between场景
2025-07-28 20:31:34,840 - INFO - 共收集 1 个未使用的overlap场景和 3 个未使用的between场景
2025-07-28 20:31:34,840 - INFO - 开始生成方案 #1
2025-07-28 20:31:34,840 - INFO - 方案 #1: 为字幕#3313选择初始化overlap场景id=3483
2025-07-28 20:31:34,840 - INFO - 方案 #1: 初始选择后，当前总时长=1.32秒
2025-07-28 20:31:34,840 - INFO - 方案 #1: 额外between选择后，当前总时长=1.32秒
2025-07-28 20:31:34,840 - INFO - 方案 #1: 额外添加between场景id=3486, 当前总时长=2.80秒
2025-07-28 20:31:34,840 - INFO - 方案 #1: 额外添加between场景id=3485, 当前总时长=3.92秒
2025-07-28 20:31:34,840 - INFO - 方案 #1: 额外添加between场景id=3484, 当前总时长=4.84秒
2025-07-28 20:31:34,840 - INFO - 方案 #1: 场景总时长(4.84秒)大于音频时长(4.43秒)，需要裁剪
2025-07-28 20:31:34,840 - INFO - 调整前总时长: 4.84秒, 目标时长: 4.43秒
2025-07-28 20:31:34,840 - INFO - 需要裁剪 0.41秒
2025-07-28 20:31:34,840 - INFO - 裁剪最长场景ID=3486：从1.48秒裁剪至1.07秒
2025-07-28 20:31:34,840 - INFO - 调整后总时长: 4.43秒，与目标时长差异: 0.00秒
2025-07-28 20:31:34,840 - INFO - 方案 #1 调整/填充后最终总时长: 4.43秒
2025-07-28 20:31:34,840 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:34,840 - INFO - 开始生成方案 #2
2025-07-28 20:31:34,840 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,840 - INFO - 开始生成方案 #3
2025-07-28 20:31:34,840 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,840 - INFO - 开始生成方案 #4
2025-07-28 20:31:34,840 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,840 - INFO - 开始生成方案 #5
2025-07-28 20:31:34,840 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,840 - INFO - 开始生成方案 #6
2025-07-28 20:31:34,840 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,840 - INFO - ========== 字幕 #44 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:34,841 - INFO - 
----- 处理字幕 #44 的方案 #1 -----
2025-07-28 20:31:34,841 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-28 20:31:34,841 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm1m0q7fx
2025-07-28 20:31:34,842 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3483.mp4 (确认存在: True)
2025-07-28 20:31:34,842 - INFO - 添加场景ID=3483，时长=1.32秒，累计时长=1.32秒
2025-07-28 20:31:34,842 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3486.mp4 (确认存在: True)
2025-07-28 20:31:34,842 - INFO - 添加场景ID=3486，时长=1.48秒，累计时长=2.80秒
2025-07-28 20:31:34,842 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3485.mp4 (确认存在: True)
2025-07-28 20:31:34,842 - INFO - 添加场景ID=3485，时长=1.12秒，累计时长=3.92秒
2025-07-28 20:31:34,842 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3484.mp4 (确认存在: True)
2025-07-28 20:31:34,842 - INFO - 添加场景ID=3484，时长=0.92秒，累计时长=4.84秒
2025-07-28 20:31:34,842 - INFO - 准备合并 4 个场景文件，总时长约 4.84秒
2025-07-28 20:31:34,842 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3483.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3486.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3485.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3484.mp4'

2025-07-28 20:31:34,842 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpm1m0q7fx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpm1m0q7fx\temp_combined.mp4
2025-07-28 20:31:35,032 - INFO - 合并后的视频时长: 4.93秒，目标音频时长: 4.43秒
2025-07-28 20:31:35,032 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpm1m0q7fx\temp_combined.mp4 -ss 0 -to 4.433 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-28 20:31:35,380 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:35,380 - INFO - 目标音频时长: 4.43秒
2025-07-28 20:31:35,380 - INFO - 实际视频时长: 4.46秒
2025-07-28 20:31:35,380 - INFO - 时长差异: 0.03秒 (0.68%)
2025-07-28 20:31:35,380 - INFO - ==========================================
2025-07-28 20:31:35,380 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:35,380 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-28 20:31:35,381 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpm1m0q7fx
2025-07-28 20:31:35,427 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:35,427 - INFO -   - 音频时长: 4.43秒
2025-07-28 20:31:35,427 - INFO -   - 视频时长: 4.46秒
2025-07-28 20:31:35,427 - INFO -   - 时长差异: 0.03秒 (0.68%)
2025-07-28 20:31:35,427 - INFO - 
字幕 #44 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:35,427 - INFO - 生成的视频文件:
2025-07-28 20:31:35,427 - INFO -   1. F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-07-28 20:31:35,427 - INFO - ========== 字幕 #44 处理结束 ==========

