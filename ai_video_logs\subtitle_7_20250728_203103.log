2025-07-28 20:31:03,928 - INFO - ========== 字幕 #7 处理开始 ==========
2025-07-28 20:31:03,928 - INFO - 字幕内容: 二伯娘被吓得魂飞魄散，哭喊着求饶。
2025-07-28 20:31:03,928 - INFO - 字幕序号: [202, 204]
2025-07-28 20:31:03,928 - INFO - 音频文件详情:
2025-07-28 20:31:03,928 - INFO -   - 路径: output\7.wav
2025-07-28 20:31:03,928 - INFO -   - 时长: 2.03秒
2025-07-28 20:31:03,928 - INFO -   - 验证音频时长: 2.03秒
2025-07-28 20:31:03,928 - INFO - 字幕时间戳信息:
2025-07-28 20:31:03,928 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:03,928 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:03,928 - INFO -   - 根据生成的音频时长(2.03秒)已调整字幕时间戳
2025-07-28 20:31:03,928 - INFO - ========== 开始为字幕 #7 生成 6 套场景方案 ==========
2025-07-28 20:31:03,929 - INFO - 开始查找字幕序号 [202, 204] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:03,929 - INFO - 找到related_overlap场景: scene_id=242, 字幕#202
2025-07-28 20:31:03,929 - INFO - 找到related_overlap场景: scene_id=243, 字幕#202
2025-07-28 20:31:03,929 - INFO - 找到related_overlap场景: scene_id=244, 字幕#204
2025-07-28 20:31:03,929 - INFO - 找到related_overlap场景: scene_id=245, 字幕#204
2025-07-28 20:31:03,930 - INFO - 字幕 #202 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:03,930 - INFO - 字幕 #204 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:03,930 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:03,930 - INFO - 开始生成方案 #1
2025-07-28 20:31:03,930 - INFO - 方案 #1: 为字幕#202选择初始化overlap场景id=242
2025-07-28 20:31:03,930 - INFO - 方案 #1: 为字幕#204选择初始化overlap场景id=245
2025-07-28 20:31:03,930 - INFO - 方案 #1: 初始选择后，当前总时长=3.32秒
2025-07-28 20:31:03,930 - INFO - 方案 #1: 额外between选择后，当前总时长=3.32秒
2025-07-28 20:31:03,930 - INFO - 方案 #1: 场景总时长(3.32秒)大于音频时长(2.03秒)，需要裁剪
2025-07-28 20:31:03,930 - INFO - 调整前总时长: 3.32秒, 目标时长: 2.03秒
2025-07-28 20:31:03,930 - INFO - 需要裁剪 1.29秒
2025-07-28 20:31:03,930 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:03,930 - INFO - 裁剪场景ID=245：从1.72秒裁剪至1.00秒
2025-07-28 20:31:03,930 - INFO - 裁剪场景ID=242：从1.60秒裁剪至1.03秒
2025-07-28 20:31:03,930 - INFO - 调整后总时长: 2.03秒，与目标时长差异: 0.00秒
2025-07-28 20:31:03,930 - INFO - 方案 #1 调整/填充后最终总时长: 2.03秒
2025-07-28 20:31:03,930 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:03,930 - INFO - 开始生成方案 #2
2025-07-28 20:31:03,930 - INFO - 方案 #2: 为字幕#202选择初始化overlap场景id=243
2025-07-28 20:31:03,930 - INFO - 方案 #2: 为字幕#204选择初始化overlap场景id=244
2025-07-28 20:31:03,930 - INFO - 方案 #2: 初始选择后，当前总时长=3.40秒
2025-07-28 20:31:03,930 - INFO - 方案 #2: 额外between选择后，当前总时长=3.40秒
2025-07-28 20:31:03,930 - INFO - 方案 #2: 场景总时长(3.40秒)大于音频时长(2.03秒)，需要裁剪
2025-07-28 20:31:03,930 - INFO - 调整前总时长: 3.40秒, 目标时长: 2.03秒
2025-07-28 20:31:03,930 - INFO - 需要裁剪 1.37秒
2025-07-28 20:31:03,930 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:03,930 - INFO - 裁剪场景ID=243：从1.92秒裁剪至1.00秒
2025-07-28 20:31:03,930 - INFO - 裁剪场景ID=244：从1.48秒裁剪至1.03秒
2025-07-28 20:31:03,930 - INFO - 调整后总时长: 2.03秒，与目标时长差异: 0.00秒
2025-07-28 20:31:03,930 - INFO - 方案 #2 调整/填充后最终总时长: 2.03秒
2025-07-28 20:31:03,930 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:03,930 - INFO - 开始生成方案 #3
2025-07-28 20:31:03,931 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,931 - INFO - 开始生成方案 #4
2025-07-28 20:31:03,931 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,931 - INFO - 开始生成方案 #5
2025-07-28 20:31:03,931 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,931 - INFO - 开始生成方案 #6
2025-07-28 20:31:03,931 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,931 - INFO - ========== 字幕 #7 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:03,931 - INFO - 
----- 处理字幕 #7 的方案 #1 -----
2025-07-28 20:31:03,931 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-28 20:31:03,931 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7fg91b_v
2025-07-28 20:31:03,931 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\242.mp4 (确认存在: True)
2025-07-28 20:31:03,931 - INFO - 添加场景ID=242，时长=1.60秒，累计时长=1.60秒
2025-07-28 20:31:03,932 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\245.mp4 (确认存在: True)
2025-07-28 20:31:03,932 - INFO - 添加场景ID=245，时长=1.72秒，累计时长=3.32秒
2025-07-28 20:31:03,932 - INFO - 场景总时长(3.32秒)已达到音频时长(2.03秒)的1.5倍，停止添加场景
2025-07-28 20:31:03,932 - INFO - 准备合并 2 个场景文件，总时长约 3.32秒
2025-07-28 20:31:03,932 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/242.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/245.mp4'

2025-07-28 20:31:03,932 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7fg91b_v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7fg91b_v\temp_combined.mp4
2025-07-28 20:31:04,057 - INFO - 合并后的视频时长: 3.37秒，目标音频时长: 2.03秒
2025-07-28 20:31:04,057 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7fg91b_v\temp_combined.mp4 -ss 0 -to 2.027 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-28 20:31:04,275 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:04,275 - INFO - 目标音频时长: 2.03秒
2025-07-28 20:31:04,275 - INFO - 实际视频时长: 2.06秒
2025-07-28 20:31:04,275 - INFO - 时长差异: 0.04秒 (1.78%)
2025-07-28 20:31:04,275 - INFO - ==========================================
2025-07-28 20:31:04,275 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:04,275 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-28 20:31:04,276 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7fg91b_v
2025-07-28 20:31:04,318 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:04,318 - INFO -   - 音频时长: 2.03秒
2025-07-28 20:31:04,318 - INFO -   - 视频时长: 2.06秒
2025-07-28 20:31:04,318 - INFO -   - 时长差异: 0.04秒 (1.78%)
2025-07-28 20:31:04,318 - INFO - 
----- 处理字幕 #7 的方案 #2 -----
2025-07-28 20:31:04,318 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-28 20:31:04,318 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5qjs15ki
2025-07-28 20:31:04,319 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\243.mp4 (确认存在: True)
2025-07-28 20:31:04,319 - INFO - 添加场景ID=243，时长=1.92秒，累计时长=1.92秒
2025-07-28 20:31:04,319 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\244.mp4 (确认存在: True)
2025-07-28 20:31:04,319 - INFO - 添加场景ID=244，时长=1.48秒，累计时长=3.40秒
2025-07-28 20:31:04,319 - INFO - 场景总时长(3.40秒)已达到音频时长(2.03秒)的1.5倍，停止添加场景
2025-07-28 20:31:04,319 - INFO - 准备合并 2 个场景文件，总时长约 3.40秒
2025-07-28 20:31:04,319 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/243.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/244.mp4'

2025-07-28 20:31:04,319 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5qjs15ki\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5qjs15ki\temp_combined.mp4
2025-07-28 20:31:04,461 - INFO - 合并后的视频时长: 3.45秒，目标音频时长: 2.03秒
2025-07-28 20:31:04,461 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5qjs15ki\temp_combined.mp4 -ss 0 -to 2.027 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-28 20:31:04,672 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:04,672 - INFO - 目标音频时长: 2.03秒
2025-07-28 20:31:04,672 - INFO - 实际视频时长: 2.06秒
2025-07-28 20:31:04,672 - INFO - 时长差异: 0.04秒 (1.78%)
2025-07-28 20:31:04,672 - INFO - ==========================================
2025-07-28 20:31:04,672 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:04,672 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-28 20:31:04,672 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5qjs15ki
2025-07-28 20:31:04,718 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:04,718 - INFO -   - 音频时长: 2.03秒
2025-07-28 20:31:04,718 - INFO -   - 视频时长: 2.06秒
2025-07-28 20:31:04,718 - INFO -   - 时长差异: 0.04秒 (1.78%)
2025-07-28 20:31:04,719 - INFO - 
字幕 #7 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:04,719 - INFO - 生成的视频文件:
2025-07-28 20:31:04,719 - INFO -   1. F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-07-28 20:31:04,719 - INFO -   2. F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-07-28 20:31:04,719 - INFO - ========== 字幕 #7 处理结束 ==========

