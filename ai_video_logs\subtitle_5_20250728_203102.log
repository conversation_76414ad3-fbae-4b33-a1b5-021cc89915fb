2025-07-28 20:31:02,925 - INFO - ========== 字幕 #5 处理开始 ==========
2025-07-28 20:31:02,925 - INFO - 字幕内容: 面对二伯娘的蛮横，女孩不再忍让，眼神一凛，单手就将她制住。
2025-07-28 20:31:02,926 - INFO - 字幕序号: [191, 194]
2025-07-28 20:31:02,926 - INFO - 音频文件详情:
2025-07-28 20:31:02,926 - INFO -   - 路径: output\5.wav
2025-07-28 20:31:02,926 - INFO -   - 时长: 3.28秒
2025-07-28 20:31:02,926 - INFO -   - 验证音频时长: 3.28秒
2025-07-28 20:31:02,926 - INFO - 字幕时间戳信息:
2025-07-28 20:31:02,926 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:02,926 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:02,926 - INFO -   - 根据生成的音频时长(3.28秒)已调整字幕时间戳
2025-07-28 20:31:02,926 - INFO - ========== 开始为字幕 #5 生成 6 套场景方案 ==========
2025-07-28 20:31:02,926 - INFO - 开始查找字幕序号 [191, 194] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:02,926 - INFO - 找到related_overlap场景: scene_id=230, 字幕#191
2025-07-28 20:31:02,926 - INFO - 找到related_overlap场景: scene_id=233, 字幕#194
2025-07-28 20:31:02,928 - INFO - 字幕 #191 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:02,928 - INFO - 字幕 #194 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:02,928 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:02,928 - INFO - 开始生成方案 #1
2025-07-28 20:31:02,928 - INFO - 方案 #1: 为字幕#191选择初始化overlap场景id=230
2025-07-28 20:31:02,928 - INFO - 方案 #1: 为字幕#194选择初始化overlap场景id=233
2025-07-28 20:31:02,928 - INFO - 方案 #1: 初始选择后，当前总时长=3.08秒
2025-07-28 20:31:02,928 - INFO - 方案 #1: 额外between选择后，当前总时长=3.08秒
2025-07-28 20:31:02,928 - INFO - 方案 #1: 场景总时长(3.08秒)小于音频时长(3.28秒)，需要延伸填充
2025-07-28 20:31:02,928 - INFO - 方案 #1: 最后一个场景ID: 233
2025-07-28 20:31:02,928 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 232
2025-07-28 20:31:02,928 - INFO - 方案 #1: 需要填充时长: 0.21秒
2025-07-28 20:31:02,928 - INFO - 方案 #1: 追加场景 scene_id=234 (裁剪至 0.21秒)
2025-07-28 20:31:02,928 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 20:31:02,928 - INFO - 方案 #1 调整/填充后最终总时长: 3.28秒
2025-07-28 20:31:02,928 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:02,928 - INFO - 开始生成方案 #2
2025-07-28 20:31:02,928 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:02,928 - INFO - 开始生成方案 #3
2025-07-28 20:31:02,928 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:02,928 - INFO - 开始生成方案 #4
2025-07-28 20:31:02,928 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:02,928 - INFO - 开始生成方案 #5
2025-07-28 20:31:02,928 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:02,928 - INFO - 开始生成方案 #6
2025-07-28 20:31:02,928 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:02,928 - INFO - ========== 字幕 #5 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:02,928 - INFO - 
----- 处理字幕 #5 的方案 #1 -----
2025-07-28 20:31:02,928 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-28 20:31:02,928 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpclg_ab9a
2025-07-28 20:31:02,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\230.mp4 (确认存在: True)
2025-07-28 20:31:02,929 - INFO - 添加场景ID=230，时长=1.76秒，累计时长=1.76秒
2025-07-28 20:31:02,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\233.mp4 (确认存在: True)
2025-07-28 20:31:02,929 - INFO - 添加场景ID=233，时长=1.32秒，累计时长=3.08秒
2025-07-28 20:31:02,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\234.mp4 (确认存在: True)
2025-07-28 20:31:02,929 - INFO - 添加场景ID=234，时长=3.68秒，累计时长=6.76秒
2025-07-28 20:31:02,929 - INFO - 场景总时长(6.76秒)已达到音频时长(3.28秒)的1.5倍，停止添加场景
2025-07-28 20:31:02,929 - INFO - 准备合并 3 个场景文件，总时长约 6.76秒
2025-07-28 20:31:02,929 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/230.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/233.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/234.mp4'

2025-07-28 20:31:02,929 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpclg_ab9a\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpclg_ab9a\temp_combined.mp4
2025-07-28 20:31:03,092 - INFO - 合并后的视频时长: 6.83秒，目标音频时长: 3.28秒
2025-07-28 20:31:03,092 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpclg_ab9a\temp_combined.mp4 -ss 0 -to 3.284 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-28 20:31:03,392 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:03,392 - INFO - 目标音频时长: 3.28秒
2025-07-28 20:31:03,392 - INFO - 实际视频时长: 3.34秒
2025-07-28 20:31:03,392 - INFO - 时长差异: 0.06秒 (1.80%)
2025-07-28 20:31:03,392 - INFO - ==========================================
2025-07-28 20:31:03,392 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:03,392 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-28 20:31:03,393 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpclg_ab9a
2025-07-28 20:31:03,432 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:03,432 - INFO -   - 音频时长: 3.28秒
2025-07-28 20:31:03,432 - INFO -   - 视频时长: 3.34秒
2025-07-28 20:31:03,432 - INFO -   - 时长差异: 0.06秒 (1.80%)
2025-07-28 20:31:03,432 - INFO - 
字幕 #5 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:03,432 - INFO - 生成的视频文件:
2025-07-28 20:31:03,432 - INFO -   1. F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-07-28 20:31:03,432 - INFO - ========== 字幕 #5 处理结束 ==========

