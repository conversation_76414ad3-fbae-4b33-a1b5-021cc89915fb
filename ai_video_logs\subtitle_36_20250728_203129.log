2025-07-28 20:31:29,128 - INFO - ========== 字幕 #36 处理开始 ==========
2025-07-28 20:31:29,128 - INFO - 字幕内容: 直到此刻，村民们才知晓，女孩救下的竟是未来的国君。
2025-07-28 20:31:29,128 - INFO - 字幕序号: [3235, 3237]
2025-07-28 20:31:29,128 - INFO - 音频文件详情:
2025-07-28 20:31:29,128 - INFO -   - 路径: output\36.wav
2025-07-28 20:31:29,128 - INFO -   - 时长: 2.65秒
2025-07-28 20:31:29,129 - INFO -   - 验证音频时长: 2.65秒
2025-07-28 20:31:29,129 - INFO - 字幕时间戳信息:
2025-07-28 20:31:29,129 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:29,129 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:29,129 - INFO -   - 根据生成的音频时长(2.65秒)已调整字幕时间戳
2025-07-28 20:31:29,129 - INFO - ========== 开始为字幕 #36 生成 6 套场景方案 ==========
2025-07-28 20:31:29,129 - INFO - 开始查找字幕序号 [3235, 3237] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:29,130 - INFO - 找到related_overlap场景: scene_id=3398, 字幕#3235
2025-07-28 20:31:29,130 - INFO - 找到related_overlap场景: scene_id=3400, 字幕#3235
2025-07-28 20:31:29,130 - INFO - 找到related_overlap场景: scene_id=3401, 字幕#3237
2025-07-28 20:31:29,131 - INFO - 字幕 #3235 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:29,131 - INFO - 字幕 #3237 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:29,131 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:29,131 - INFO - 开始生成方案 #1
2025-07-28 20:31:29,131 - INFO - 方案 #1: 为字幕#3235选择初始化overlap场景id=3398
2025-07-28 20:31:29,131 - INFO - 方案 #1: 为字幕#3237选择初始化overlap场景id=3401
2025-07-28 20:31:29,131 - INFO - 方案 #1: 初始选择后，当前总时长=3.36秒
2025-07-28 20:31:29,131 - INFO - 方案 #1: 额外between选择后，当前总时长=3.36秒
2025-07-28 20:31:29,131 - INFO - 方案 #1: 场景总时长(3.36秒)大于音频时长(2.65秒)，需要裁剪
2025-07-28 20:31:29,131 - INFO - 调整前总时长: 3.36秒, 目标时长: 2.65秒
2025-07-28 20:31:29,131 - INFO - 需要裁剪 0.71秒
2025-07-28 20:31:29,131 - INFO - 裁剪最长场景ID=3401：从2.16秒裁剪至1.45秒
2025-07-28 20:31:29,131 - INFO - 调整后总时长: 2.65秒，与目标时长差异: 0.00秒
2025-07-28 20:31:29,132 - INFO - 方案 #1 调整/填充后最终总时长: 2.65秒
2025-07-28 20:31:29,132 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:29,132 - INFO - 开始生成方案 #2
2025-07-28 20:31:29,132 - INFO - 方案 #2: 为字幕#3235选择初始化overlap场景id=3400
2025-07-28 20:31:29,132 - INFO - 方案 #2: 初始选择后，当前总时长=2.04秒
2025-07-28 20:31:29,132 - INFO - 方案 #2: 额外between选择后，当前总时长=2.04秒
2025-07-28 20:31:29,132 - INFO - 方案 #2: 场景总时长(2.04秒)小于音频时长(2.65秒)，需要延伸填充
2025-07-28 20:31:29,132 - INFO - 方案 #2: 最后一个场景ID: 3400
2025-07-28 20:31:29,132 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 3399
2025-07-28 20:31:29,132 - INFO - 方案 #2: 需要填充时长: 0.61秒
2025-07-28 20:31:29,132 - INFO - 方案 #2: 跳过已使用的场景: scene_id=3401
2025-07-28 20:31:29,132 - INFO - 方案 #2: 追加场景 scene_id=3402 (裁剪至 0.61秒)
2025-07-28 20:31:29,132 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:29,132 - INFO - 方案 #2 调整/填充后最终总时长: 2.65秒
2025-07-28 20:31:29,132 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:29,132 - INFO - 开始生成方案 #3
2025-07-28 20:31:29,132 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:29,132 - INFO - 开始生成方案 #4
2025-07-28 20:31:29,132 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:29,132 - INFO - 开始生成方案 #5
2025-07-28 20:31:29,132 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:29,133 - INFO - 开始生成方案 #6
2025-07-28 20:31:29,133 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:29,133 - INFO - ========== 字幕 #36 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:29,133 - INFO - 
----- 处理字幕 #36 的方案 #1 -----
2025-07-28 20:31:29,133 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-28 20:31:29,133 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpf1xga5a3
2025-07-28 20:31:29,134 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3398.mp4 (确认存在: True)
2025-07-28 20:31:29,134 - INFO - 添加场景ID=3398，时长=1.20秒，累计时长=1.20秒
2025-07-28 20:31:29,134 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3401.mp4 (确认存在: True)
2025-07-28 20:31:29,134 - INFO - 添加场景ID=3401，时长=2.16秒，累计时长=3.36秒
2025-07-28 20:31:29,134 - INFO - 准备合并 2 个场景文件，总时长约 3.36秒
2025-07-28 20:31:29,134 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3398.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3401.mp4'

2025-07-28 20:31:29,134 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpf1xga5a3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpf1xga5a3\temp_combined.mp4
2025-07-28 20:31:29,280 - INFO - 合并后的视频时长: 3.41秒，目标音频时长: 2.65秒
2025-07-28 20:31:29,280 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpf1xga5a3\temp_combined.mp4 -ss 0 -to 2.65 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-28 20:31:29,519 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:29,519 - INFO - 目标音频时长: 2.65秒
2025-07-28 20:31:29,519 - INFO - 实际视频时长: 2.70秒
2025-07-28 20:31:29,519 - INFO - 时长差异: 0.05秒 (2.00%)
2025-07-28 20:31:29,519 - INFO - ==========================================
2025-07-28 20:31:29,519 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:29,519 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-28 20:31:29,520 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpf1xga5a3
2025-07-28 20:31:29,561 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:29,561 - INFO -   - 音频时长: 2.65秒
2025-07-28 20:31:29,561 - INFO -   - 视频时长: 2.70秒
2025-07-28 20:31:29,561 - INFO -   - 时长差异: 0.05秒 (2.00%)
2025-07-28 20:31:29,561 - INFO - 
----- 处理字幕 #36 的方案 #2 -----
2025-07-28 20:31:29,561 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-28 20:31:29,562 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdelq9999
2025-07-28 20:31:29,562 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3400.mp4 (确认存在: True)
2025-07-28 20:31:29,562 - INFO - 添加场景ID=3400，时长=2.04秒，累计时长=2.04秒
2025-07-28 20:31:29,563 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3402.mp4 (确认存在: True)
2025-07-28 20:31:29,563 - INFO - 添加场景ID=3402，时长=1.16秒，累计时长=3.20秒
2025-07-28 20:31:29,563 - INFO - 准备合并 2 个场景文件，总时长约 3.20秒
2025-07-28 20:31:29,563 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3400.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3402.mp4'

2025-07-28 20:31:29,563 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdelq9999\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdelq9999\temp_combined.mp4
2025-07-28 20:31:29,702 - INFO - 合并后的视频时长: 3.22秒，目标音频时长: 2.65秒
2025-07-28 20:31:29,702 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdelq9999\temp_combined.mp4 -ss 0 -to 2.65 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-28 20:31:29,964 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:29,964 - INFO - 目标音频时长: 2.65秒
2025-07-28 20:31:29,964 - INFO - 实际视频时长: 2.70秒
2025-07-28 20:31:29,964 - INFO - 时长差异: 0.05秒 (2.00%)
2025-07-28 20:31:29,964 - INFO - ==========================================
2025-07-28 20:31:29,964 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:29,964 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-28 20:31:29,965 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdelq9999
2025-07-28 20:31:30,007 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:30,007 - INFO -   - 音频时长: 2.65秒
2025-07-28 20:31:30,007 - INFO -   - 视频时长: 2.70秒
2025-07-28 20:31:30,007 - INFO -   - 时长差异: 0.05秒 (2.00%)
2025-07-28 20:31:30,007 - INFO - 
字幕 #36 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:30,007 - INFO - 生成的视频文件:
2025-07-28 20:31:30,007 - INFO -   1. F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-07-28 20:31:30,007 - INFO -   2. F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-07-28 20:31:30,007 - INFO - ========== 字幕 #36 处理结束 ==========

