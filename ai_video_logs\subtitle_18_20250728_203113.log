2025-07-28 20:31:13,086 - INFO - ========== 字幕 #18 处理开始 ==========
2025-07-28 20:31:13,086 - INFO - 字幕内容: 危机预警突然响起，系统提示熊瞎子即将袭击村庄。
2025-07-28 20:31:13,086 - INFO - 字幕序号: [1638, 1642]
2025-07-28 20:31:13,086 - INFO - 音频文件详情:
2025-07-28 20:31:13,086 - INFO -   - 路径: output\18.wav
2025-07-28 20:31:13,086 - INFO -   - 时长: 2.82秒
2025-07-28 20:31:13,086 - INFO -   - 验证音频时长: 2.82秒
2025-07-28 20:31:13,086 - INFO - 字幕时间戳信息:
2025-07-28 20:31:13,086 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:13,087 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:13,087 - INFO -   - 根据生成的音频时长(2.82秒)已调整字幕时间戳
2025-07-28 20:31:13,087 - INFO - ========== 开始为字幕 #18 生成 6 套场景方案 ==========
2025-07-28 20:31:13,087 - INFO - 开始查找字幕序号 [1638, 1642] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:13,087 - INFO - 找到related_overlap场景: scene_id=1708, 字幕#1638
2025-07-28 20:31:13,087 - INFO - 找到related_overlap场景: scene_id=1709, 字幕#1642
2025-07-28 20:31:13,088 - INFO - 找到related_between场景: scene_id=1704, 字幕#1638
2025-07-28 20:31:13,088 - INFO - 找到related_between场景: scene_id=1705, 字幕#1638
2025-07-28 20:31:13,088 - INFO - 找到related_between场景: scene_id=1706, 字幕#1638
2025-07-28 20:31:13,088 - INFO - 找到related_between场景: scene_id=1707, 字幕#1638
2025-07-28 20:31:13,088 - INFO - 字幕 #1638 找到 1 个overlap场景, 4 个between场景
2025-07-28 20:31:13,088 - INFO - 字幕 #1642 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:13,088 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-07-28 20:31:13,088 - INFO - 开始生成方案 #1
2025-07-28 20:31:13,088 - INFO - 方案 #1: 为字幕#1638选择初始化overlap场景id=1708
2025-07-28 20:31:13,088 - INFO - 方案 #1: 为字幕#1642选择初始化overlap场景id=1709
2025-07-28 20:31:13,088 - INFO - 方案 #1: 初始选择后，当前总时长=5.88秒
2025-07-28 20:31:13,088 - INFO - 方案 #1: 额外between选择后，当前总时长=5.88秒
2025-07-28 20:31:13,088 - INFO - 方案 #1: 场景总时长(5.88秒)大于音频时长(2.82秒)，需要裁剪
2025-07-28 20:31:13,088 - INFO - 调整前总时长: 5.88秒, 目标时长: 2.82秒
2025-07-28 20:31:13,088 - INFO - 需要裁剪 3.06秒
2025-07-28 20:31:13,088 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:13,088 - INFO - 裁剪场景ID=1709：从3.04秒裁剪至1.00秒
2025-07-28 20:31:13,088 - INFO - 裁剪场景ID=1708：从2.84秒裁剪至1.82秒
2025-07-28 20:31:13,088 - INFO - 调整后总时长: 2.82秒，与目标时长差异: 0.00秒
2025-07-28 20:31:13,088 - INFO - 方案 #1 调整/填充后最终总时长: 2.82秒
2025-07-28 20:31:13,088 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:13,088 - INFO - 开始生成方案 #2
2025-07-28 20:31:13,088 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:13,088 - INFO - 方案 #2: 为字幕#1638选择初始化between场景id=1704
2025-07-28 20:31:13,088 - INFO - 方案 #2: 额外between选择后，当前总时长=2.04秒
2025-07-28 20:31:13,088 - INFO - 方案 #2: 额外添加between场景id=1705, 当前总时长=4.28秒
2025-07-28 20:31:13,089 - INFO - 方案 #2: 场景总时长(4.28秒)大于音频时长(2.82秒)，需要裁剪
2025-07-28 20:31:13,089 - INFO - 调整前总时长: 4.28秒, 目标时长: 2.82秒
2025-07-28 20:31:13,089 - INFO - 需要裁剪 1.46秒
2025-07-28 20:31:13,089 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:13,089 - INFO - 裁剪场景ID=1705：从2.24秒裁剪至1.00秒
2025-07-28 20:31:13,089 - INFO - 裁剪场景ID=1704：从2.04秒裁剪至1.82秒
2025-07-28 20:31:13,089 - INFO - 调整后总时长: 2.82秒，与目标时长差异: 0.00秒
2025-07-28 20:31:13,089 - INFO - 方案 #2 调整/填充后最终总时长: 2.82秒
2025-07-28 20:31:13,089 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:13,089 - INFO - 开始生成方案 #3
2025-07-28 20:31:13,089 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:13,089 - INFO - 方案 #3: 为字幕#1638选择初始化between场景id=1707
2025-07-28 20:31:13,089 - INFO - 方案 #3: 额外between选择后，当前总时长=0.16秒
2025-07-28 20:31:13,089 - INFO - 方案 #3: 额外添加between场景id=1706, 当前总时长=2.04秒
2025-07-28 20:31:13,089 - INFO - 方案 #3: 场景总时长(2.04秒)小于音频时长(2.82秒)，需要延伸填充
2025-07-28 20:31:13,089 - INFO - 方案 #3: 最后一个场景ID: 1706
2025-07-28 20:31:13,089 - INFO - 方案 #3: 找到最后一个场景在原始列表中的索引: 1705
2025-07-28 20:31:13,089 - INFO - 方案 #3: 需要填充时长: 0.78秒
2025-07-28 20:31:13,089 - INFO - 方案 #3: 跳过已使用的场景: scene_id=1707
2025-07-28 20:31:13,089 - INFO - 方案 #3: 跳过已使用的场景: scene_id=1708
2025-07-28 20:31:13,089 - INFO - 方案 #3: 跳过已使用的场景: scene_id=1709
2025-07-28 20:31:13,089 - INFO - 方案 #3: 追加场景 scene_id=1710 (裁剪至 0.78秒)
2025-07-28 20:31:13,089 - INFO - 方案 #3: 成功填充至目标时长
2025-07-28 20:31:13,089 - INFO - 方案 #3 调整/填充后最终总时长: 2.82秒
2025-07-28 20:31:13,089 - INFO - 方案 #3 添加到方案列表
2025-07-28 20:31:13,089 - INFO - 开始生成方案 #4
2025-07-28 20:31:13,089 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:13,089 - INFO - 开始生成方案 #5
2025-07-28 20:31:13,089 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:13,089 - INFO - 开始生成方案 #6
2025-07-28 20:31:13,089 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:13,089 - INFO - ========== 字幕 #18 的 3 套有效场景方案生成完成 ==========
2025-07-28 20:31:13,089 - INFO - 
----- 处理字幕 #18 的方案 #1 -----
2025-07-28 20:31:13,089 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-28 20:31:13,089 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ljiv47j
2025-07-28 20:31:13,090 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1708.mp4 (确认存在: True)
2025-07-28 20:31:13,090 - INFO - 添加场景ID=1708，时长=2.84秒，累计时长=2.84秒
2025-07-28 20:31:13,090 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1709.mp4 (确认存在: True)
2025-07-28 20:31:13,090 - INFO - 添加场景ID=1709，时长=3.04秒，累计时长=5.88秒
2025-07-28 20:31:13,090 - INFO - 场景总时长(5.88秒)已达到音频时长(2.82秒)的1.5倍，停止添加场景
2025-07-28 20:31:13,090 - INFO - 准备合并 2 个场景文件，总时长约 5.88秒
2025-07-28 20:31:13,090 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1708.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1709.mp4'

2025-07-28 20:31:13,090 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8ljiv47j\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8ljiv47j\temp_combined.mp4
2025-07-28 20:31:13,227 - INFO - 合并后的视频时长: 5.93秒，目标音频时长: 2.82秒
2025-07-28 20:31:13,227 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8ljiv47j\temp_combined.mp4 -ss 0 -to 2.816 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-28 20:31:13,495 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:13,495 - INFO - 目标音频时长: 2.82秒
2025-07-28 20:31:13,495 - INFO - 实际视频时长: 2.86秒
2025-07-28 20:31:13,495 - INFO - 时长差异: 0.05秒 (1.67%)
2025-07-28 20:31:13,495 - INFO - ==========================================
2025-07-28 20:31:13,495 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:13,495 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-28 20:31:13,496 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ljiv47j
2025-07-28 20:31:13,542 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:13,542 - INFO -   - 音频时长: 2.82秒
2025-07-28 20:31:13,542 - INFO -   - 视频时长: 2.86秒
2025-07-28 20:31:13,542 - INFO -   - 时长差异: 0.05秒 (1.67%)
2025-07-28 20:31:13,542 - INFO - 
----- 处理字幕 #18 的方案 #2 -----
2025-07-28 20:31:13,542 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-28 20:31:13,542 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplyx2dxf6
2025-07-28 20:31:13,544 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1704.mp4 (确认存在: True)
2025-07-28 20:31:13,544 - INFO - 添加场景ID=1704，时长=2.04秒，累计时长=2.04秒
2025-07-28 20:31:13,544 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1705.mp4 (确认存在: True)
2025-07-28 20:31:13,544 - INFO - 添加场景ID=1705，时长=2.24秒，累计时长=4.28秒
2025-07-28 20:31:13,544 - INFO - 场景总时长(4.28秒)已达到音频时长(2.82秒)的1.5倍，停止添加场景
2025-07-28 20:31:13,544 - INFO - 准备合并 2 个场景文件，总时长约 4.28秒
2025-07-28 20:31:13,544 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1704.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1705.mp4'

2025-07-28 20:31:13,544 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplyx2dxf6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplyx2dxf6\temp_combined.mp4
2025-07-28 20:31:13,655 - INFO - 合并后的视频时长: 4.33秒，目标音频时长: 2.82秒
2025-07-28 20:31:13,655 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplyx2dxf6\temp_combined.mp4 -ss 0 -to 2.816 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-28 20:31:13,907 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:13,908 - INFO - 目标音频时长: 2.82秒
2025-07-28 20:31:13,908 - INFO - 实际视频时长: 2.86秒
2025-07-28 20:31:13,908 - INFO - 时长差异: 0.05秒 (1.67%)
2025-07-28 20:31:13,908 - INFO - ==========================================
2025-07-28 20:31:13,908 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:13,908 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-28 20:31:13,908 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplyx2dxf6
2025-07-28 20:31:13,948 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:13,948 - INFO -   - 音频时长: 2.82秒
2025-07-28 20:31:13,948 - INFO -   - 视频时长: 2.86秒
2025-07-28 20:31:13,948 - INFO -   - 时长差异: 0.05秒 (1.67%)
2025-07-28 20:31:13,948 - INFO - 
----- 处理字幕 #18 的方案 #3 -----
2025-07-28 20:31:13,948 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-28 20:31:13,948 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppcl5_ui5
2025-07-28 20:31:13,948 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1707.mp4 (确认存在: True)
2025-07-28 20:31:13,949 - INFO - 添加场景ID=1707，时长=0.16秒，累计时长=0.16秒
2025-07-28 20:31:13,949 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1706.mp4 (确认存在: True)
2025-07-28 20:31:13,949 - INFO - 添加场景ID=1706，时长=1.88秒，累计时长=2.04秒
2025-07-28 20:31:13,949 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1710.mp4 (确认存在: True)
2025-07-28 20:31:13,949 - INFO - 添加场景ID=1710，时长=2.48秒，累计时长=4.52秒
2025-07-28 20:31:13,949 - INFO - 场景总时长(4.52秒)已达到音频时长(2.82秒)的1.5倍，停止添加场景
2025-07-28 20:31:13,949 - INFO - 准备合并 3 个场景文件，总时长约 4.52秒
2025-07-28 20:31:13,949 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1707.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1706.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1710.mp4'

2025-07-28 20:31:13,949 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppcl5_ui5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppcl5_ui5\temp_combined.mp4
2025-07-28 20:31:14,090 - INFO - 合并后的视频时长: 4.59秒，目标音频时长: 2.82秒
2025-07-28 20:31:14,090 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppcl5_ui5\temp_combined.mp4 -ss 0 -to 2.816 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-28 20:31:14,344 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:14,344 - INFO - 目标音频时长: 2.82秒
2025-07-28 20:31:14,344 - INFO - 实际视频时长: 2.86秒
2025-07-28 20:31:14,344 - INFO - 时长差异: 0.05秒 (1.67%)
2025-07-28 20:31:14,344 - INFO - ==========================================
2025-07-28 20:31:14,344 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:14,344 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-28 20:31:14,345 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppcl5_ui5
2025-07-28 20:31:14,385 - INFO - 方案 #3 处理完成:
2025-07-28 20:31:14,385 - INFO -   - 音频时长: 2.82秒
2025-07-28 20:31:14,385 - INFO -   - 视频时长: 2.86秒
2025-07-28 20:31:14,385 - INFO -   - 时长差异: 0.05秒 (1.67%)
2025-07-28 20:31:14,385 - INFO - 
字幕 #18 处理完成，成功生成 3/3 套方案
2025-07-28 20:31:14,385 - INFO - 生成的视频文件:
2025-07-28 20:31:14,385 - INFO -   1. F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-07-28 20:31:14,386 - INFO -   2. F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-07-28 20:31:14,386 - INFO -   3. F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-07-28 20:31:14,386 - INFO - ========== 字幕 #18 处理结束 ==========

