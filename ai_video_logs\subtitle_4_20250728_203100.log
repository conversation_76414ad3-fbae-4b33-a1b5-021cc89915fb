2025-07-28 20:31:00,065 - INFO - ========== 字幕 #4 处理开始 ==========
2025-07-28 20:31:00,065 - INFO - 字幕内容: 就在此刻，原本瘦弱的女孩体内天生神力觉醒，更绑定了神力系统。
2025-07-28 20:31:00,065 - INFO - 字幕序号: [80, 82]
2025-07-28 20:31:00,065 - INFO - 音频文件详情:
2025-07-28 20:31:00,065 - INFO -   - 路径: output\4.wav
2025-07-28 20:31:00,065 - INFO -   - 时长: 3.64秒
2025-07-28 20:31:00,065 - INFO -   - 验证音频时长: 3.64秒
2025-07-28 20:31:00,065 - INFO - 字幕时间戳信息:
2025-07-28 20:31:00,065 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:00,065 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:00,065 - INFO -   - 根据生成的音频时长(3.64秒)已调整字幕时间戳
2025-07-28 20:31:00,066 - INFO - ========== 开始为字幕 #4 生成 6 套场景方案 ==========
2025-07-28 20:31:00,066 - INFO - 开始查找字幕序号 [80, 82] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:00,066 - INFO - 找到related_overlap场景: scene_id=103, 字幕#80
2025-07-28 20:31:00,066 - INFO - 找到related_overlap场景: scene_id=104, 字幕#82
2025-07-28 20:31:00,066 - INFO - 找到related_overlap场景: scene_id=105, 字幕#82
2025-07-28 20:31:00,066 - INFO - 找到related_between场景: scene_id=88, 字幕#80
2025-07-28 20:31:00,066 - INFO - 找到related_between场景: scene_id=89, 字幕#80
2025-07-28 20:31:00,066 - INFO - 找到related_between场景: scene_id=90, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=91, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=92, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=93, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=94, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=95, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=96, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=97, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=98, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=99, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=100, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=101, 字幕#80
2025-07-28 20:31:00,067 - INFO - 找到related_between场景: scene_id=102, 字幕#80
2025-07-28 20:31:00,067 - INFO - 字幕 #80 找到 1 个overlap场景, 15 个between场景
2025-07-28 20:31:00,067 - INFO - 字幕 #82 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:00,067 - INFO - 共收集 3 个未使用的overlap场景和 15 个未使用的between场景
2025-07-28 20:31:00,067 - INFO - 开始生成方案 #1
2025-07-28 20:31:00,067 - INFO - 方案 #1: 为字幕#80选择初始化overlap场景id=103
2025-07-28 20:31:00,067 - INFO - 方案 #1: 为字幕#82选择初始化overlap场景id=104
2025-07-28 20:31:00,067 - INFO - 方案 #1: 初始选择后，当前总时长=7.36秒
2025-07-28 20:31:00,067 - INFO - 方案 #1: 额外between选择后，当前总时长=7.36秒
2025-07-28 20:31:00,067 - INFO - 方案 #1: 场景总时长(7.36秒)大于音频时长(3.64秒)，需要裁剪
2025-07-28 20:31:00,067 - INFO - 调整前总时长: 7.36秒, 目标时长: 3.64秒
2025-07-28 20:31:00,067 - INFO - 需要裁剪 3.72秒
2025-07-28 20:31:00,067 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:00,068 - INFO - 裁剪场景ID=103：从4.76秒裁剪至1.43秒
2025-07-28 20:31:00,068 - INFO - 裁剪场景ID=104：从2.60秒裁剪至2.21秒
2025-07-28 20:31:00,068 - INFO - 调整后总时长: 3.64秒，与目标时长差异: 0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #1 调整/填充后最终总时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:00,068 - INFO - 开始生成方案 #2
2025-07-28 20:31:00,068 - INFO - 方案 #2: 为字幕#82选择初始化overlap场景id=105
2025-07-28 20:31:00,068 - INFO - 方案 #2: 初始选择后，当前总时长=3.60秒
2025-07-28 20:31:00,068 - INFO - 方案 #2: 为字幕#80选择初始化between场景id=102
2025-07-28 20:31:00,068 - INFO - 方案 #2: 额外between选择后，当前总时长=5.04秒
2025-07-28 20:31:00,068 - INFO - 方案 #2: 场景总时长(5.04秒)大于音频时长(3.64秒)，需要裁剪
2025-07-28 20:31:00,068 - INFO - 调整前总时长: 5.04秒, 目标时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 需要裁剪 1.40秒
2025-07-28 20:31:00,068 - INFO - 裁剪最长场景ID=105：从3.60秒裁剪至2.20秒
2025-07-28 20:31:00,068 - INFO - 调整后总时长: 3.64秒，与目标时长差异: 0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #2 调整/填充后最终总时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:00,068 - INFO - 开始生成方案 #3
2025-07-28 20:31:00,068 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #3: 为字幕#80选择初始化between场景id=92
2025-07-28 20:31:00,068 - INFO - 方案 #3: 额外between选择后，当前总时长=4.12秒
2025-07-28 20:31:00,068 - INFO - 方案 #3: 场景总时长(4.12秒)大于音频时长(3.64秒)，需要裁剪
2025-07-28 20:31:00,068 - INFO - 调整前总时长: 4.12秒, 目标时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 需要裁剪 0.48秒
2025-07-28 20:31:00,068 - INFO - 裁剪最长场景ID=92：从4.12秒裁剪至3.64秒
2025-07-28 20:31:00,068 - INFO - 调整后总时长: 3.64秒，与目标时长差异: 0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #3 调整/填充后最终总时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 方案 #3 添加到方案列表
2025-07-28 20:31:00,068 - INFO - 开始生成方案 #4
2025-07-28 20:31:00,068 - INFO - 方案 #4: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #4: 为字幕#80选择初始化between场景id=98
2025-07-28 20:31:00,068 - INFO - 方案 #4: 额外between选择后，当前总时长=1.32秒
2025-07-28 20:31:00,068 - INFO - 方案 #4: 额外添加between场景id=99, 当前总时长=4.08秒
2025-07-28 20:31:00,068 - INFO - 方案 #4: 场景总时长(4.08秒)大于音频时长(3.64秒)，需要裁剪
2025-07-28 20:31:00,068 - INFO - 调整前总时长: 4.08秒, 目标时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 需要裁剪 0.44秒
2025-07-28 20:31:00,068 - INFO - 裁剪最长场景ID=99：从2.76秒裁剪至2.32秒
2025-07-28 20:31:00,068 - INFO - 调整后总时长: 3.64秒，与目标时长差异: 0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #4 调整/填充后最终总时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 方案 #4 添加到方案列表
2025-07-28 20:31:00,068 - INFO - 开始生成方案 #5
2025-07-28 20:31:00,068 - INFO - 方案 #5: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #5: 为字幕#80选择初始化between场景id=89
2025-07-28 20:31:00,068 - INFO - 方案 #5: 额外between选择后，当前总时长=1.68秒
2025-07-28 20:31:00,068 - INFO - 方案 #5: 额外添加between场景id=90, 当前总时长=3.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #5: 额外添加between场景id=93, 当前总时长=5.16秒
2025-07-28 20:31:00,068 - INFO - 方案 #5: 场景总时长(5.16秒)大于音频时长(3.64秒)，需要裁剪
2025-07-28 20:31:00,068 - INFO - 调整前总时长: 5.16秒, 目标时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 需要裁剪 1.52秒
2025-07-28 20:31:00,068 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:00,068 - INFO - 裁剪场景ID=93：从2.16秒裁剪至1.00秒
2025-07-28 20:31:00,068 - INFO - 裁剪场景ID=89：从1.68秒裁剪至1.32秒
2025-07-28 20:31:00,068 - INFO - 调整后总时长: 3.64秒，与目标时长差异: 0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #5 调整/填充后最终总时长: 3.64秒
2025-07-28 20:31:00,068 - INFO - 方案 #5 添加到方案列表
2025-07-28 20:31:00,068 - INFO - 开始生成方案 #6
2025-07-28 20:31:00,068 - INFO - 方案 #6: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:00,068 - INFO - 方案 #6: 为字幕#80选择初始化between场景id=95
2025-07-28 20:31:00,068 - INFO - 方案 #6: 额外between选择后，当前总时长=0.92秒
2025-07-28 20:31:00,068 - INFO - 方案 #6: 额外添加between场景id=88, 当前总时长=2.20秒
2025-07-28 20:31:00,069 - INFO - 方案 #6: 额外添加between场景id=96, 当前总时长=3.20秒
2025-07-28 20:31:00,069 - INFO - 方案 #6: 额外添加between场景id=97, 当前总时长=4.56秒
2025-07-28 20:31:00,069 - INFO - 方案 #6: 场景总时长(4.56秒)大于音频时长(3.64秒)，需要裁剪
2025-07-28 20:31:00,069 - INFO - 调整前总时长: 4.56秒, 目标时长: 3.64秒
2025-07-28 20:31:00,069 - INFO - 需要裁剪 0.92秒
2025-07-28 20:31:00,069 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:00,069 - INFO - 裁剪场景ID=97：从1.36秒裁剪至1.00秒
2025-07-28 20:31:00,069 - INFO - 裁剪场景ID=88：从1.28秒裁剪至1.00秒
2025-07-28 20:31:00,069 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.28秒
2025-07-28 20:31:00,069 - INFO - 移除场景ID=95，时长=0.92秒
2025-07-28 20:31:00,069 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.64秒
2025-07-28 20:31:00,069 - INFO - 方案 #6 调整/填充后最终总时长: 3.00秒
2025-07-28 20:31:00,069 - INFO - 方案 #6 添加到方案列表
2025-07-28 20:31:00,069 - INFO - ========== 字幕 #4 的 6 套有效场景方案生成完成 ==========
2025-07-28 20:31:00,069 - INFO - 
----- 处理字幕 #4 的方案 #1 -----
2025-07-28 20:31:00,069 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-28 20:31:00,069 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbp1om3ex
2025-07-28 20:31:00,069 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\103.mp4 (确认存在: True)
2025-07-28 20:31:00,069 - INFO - 添加场景ID=103，时长=4.76秒，累计时长=4.76秒
2025-07-28 20:31:00,069 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\104.mp4 (确认存在: True)
2025-07-28 20:31:00,069 - INFO - 添加场景ID=104，时长=2.60秒，累计时长=7.36秒
2025-07-28 20:31:00,069 - INFO - 场景总时长(7.36秒)已达到音频时长(3.64秒)的1.5倍，停止添加场景
2025-07-28 20:31:00,069 - INFO - 准备合并 2 个场景文件，总时长约 7.36秒
2025-07-28 20:31:00,069 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/103.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/104.mp4'

2025-07-28 20:31:00,071 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbp1om3ex\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbp1om3ex\temp_combined.mp4
2025-07-28 20:31:00,196 - INFO - 合并后的视频时长: 7.41秒，目标音频时长: 3.64秒
2025-07-28 20:31:00,196 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbp1om3ex\temp_combined.mp4 -ss 0 -to 3.637 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-28 20:31:00,473 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:00,473 - INFO - 目标音频时长: 3.64秒
2025-07-28 20:31:00,473 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:00,473 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:00,473 - INFO - ==========================================
2025-07-28 20:31:00,473 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:00,473 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-28 20:31:00,473 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbp1om3ex
2025-07-28 20:31:00,513 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:00,513 - INFO -   - 音频时长: 3.64秒
2025-07-28 20:31:00,513 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:00,513 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:00,513 - INFO - 
----- 处理字幕 #4 的方案 #2 -----
2025-07-28 20:31:00,513 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-28 20:31:00,513 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_xdb_o2m
2025-07-28 20:31:00,513 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\105.mp4 (确认存在: True)
2025-07-28 20:31:00,513 - INFO - 添加场景ID=105，时长=3.60秒，累计时长=3.60秒
2025-07-28 20:31:00,513 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\102.mp4 (确认存在: True)
2025-07-28 20:31:00,514 - INFO - 添加场景ID=102，时长=1.44秒，累计时长=5.04秒
2025-07-28 20:31:00,514 - INFO - 准备合并 2 个场景文件，总时长约 5.04秒
2025-07-28 20:31:00,514 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/105.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/102.mp4'

2025-07-28 20:31:00,514 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_xdb_o2m\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_xdb_o2m\temp_combined.mp4
2025-07-28 20:31:00,663 - INFO - 合并后的视频时长: 5.09秒，目标音频时长: 3.64秒
2025-07-28 20:31:00,663 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_xdb_o2m\temp_combined.mp4 -ss 0 -to 3.637 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-28 20:31:00,928 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:00,928 - INFO - 目标音频时长: 3.64秒
2025-07-28 20:31:00,928 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:00,928 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:00,928 - INFO - ==========================================
2025-07-28 20:31:00,928 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:00,928 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-28 20:31:00,929 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_xdb_o2m
2025-07-28 20:31:00,975 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:00,975 - INFO -   - 音频时长: 3.64秒
2025-07-28 20:31:00,975 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:00,975 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:00,975 - INFO - 
----- 处理字幕 #4 的方案 #3 -----
2025-07-28 20:31:00,975 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-28 20:31:00,975 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe5cdqrxi
2025-07-28 20:31:00,976 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\92.mp4 (确认存在: True)
2025-07-28 20:31:00,976 - INFO - 添加场景ID=92，时长=4.12秒，累计时长=4.12秒
2025-07-28 20:31:00,976 - INFO - 准备合并 1 个场景文件，总时长约 4.12秒
2025-07-28 20:31:00,976 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/92.mp4'

2025-07-28 20:31:00,976 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe5cdqrxi\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe5cdqrxi\temp_combined.mp4
2025-07-28 20:31:01,094 - INFO - 合并后的视频时长: 4.14秒，目标音频时长: 3.64秒
2025-07-28 20:31:01,094 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe5cdqrxi\temp_combined.mp4 -ss 0 -to 3.637 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-28 20:31:01,387 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:01,387 - INFO - 目标音频时长: 3.64秒
2025-07-28 20:31:01,387 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:01,387 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:01,387 - INFO - ==========================================
2025-07-28 20:31:01,387 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:01,387 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-28 20:31:01,388 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe5cdqrxi
2025-07-28 20:31:01,429 - INFO - 方案 #3 处理完成:
2025-07-28 20:31:01,429 - INFO -   - 音频时长: 3.64秒
2025-07-28 20:31:01,429 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:01,429 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:01,429 - INFO - 
----- 处理字幕 #4 的方案 #4 -----
2025-07-28 20:31:01,429 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_4.mp4
2025-07-28 20:31:01,430 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp39uchpsp
2025-07-28 20:31:01,430 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\98.mp4 (确认存在: True)
2025-07-28 20:31:01,430 - INFO - 添加场景ID=98，时长=1.32秒，累计时长=1.32秒
2025-07-28 20:31:01,430 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\99.mp4 (确认存在: True)
2025-07-28 20:31:01,430 - INFO - 添加场景ID=99，时长=2.76秒，累计时长=4.08秒
2025-07-28 20:31:01,430 - INFO - 准备合并 2 个场景文件，总时长约 4.08秒
2025-07-28 20:31:01,430 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/98.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/99.mp4'

2025-07-28 20:31:01,431 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp39uchpsp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp39uchpsp\temp_combined.mp4
2025-07-28 20:31:01,576 - INFO - 合并后的视频时长: 4.13秒，目标音频时长: 3.64秒
2025-07-28 20:31:01,576 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp39uchpsp\temp_combined.mp4 -ss 0 -to 3.637 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_4.mp4
2025-07-28 20:31:01,873 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:01,873 - INFO - 目标音频时长: 3.64秒
2025-07-28 20:31:01,873 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:01,873 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:01,873 - INFO - ==========================================
2025-07-28 20:31:01,873 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:01,873 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_4.mp4
2025-07-28 20:31:01,873 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp39uchpsp
2025-07-28 20:31:01,917 - INFO - 方案 #4 处理完成:
2025-07-28 20:31:01,917 - INFO -   - 音频时长: 3.64秒
2025-07-28 20:31:01,917 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:01,917 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:01,917 - INFO - 
----- 处理字幕 #4 的方案 #5 -----
2025-07-28 20:31:01,918 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_5.mp4
2025-07-28 20:31:01,918 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp69_uuwfd
2025-07-28 20:31:01,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\89.mp4 (确认存在: True)
2025-07-28 20:31:01,919 - INFO - 添加场景ID=89，时长=1.68秒，累计时长=1.68秒
2025-07-28 20:31:01,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\90.mp4 (确认存在: True)
2025-07-28 20:31:01,919 - INFO - 添加场景ID=90，时长=1.32秒，累计时长=3.00秒
2025-07-28 20:31:01,919 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\93.mp4 (确认存在: True)
2025-07-28 20:31:01,919 - INFO - 添加场景ID=93，时长=2.16秒，累计时长=5.16秒
2025-07-28 20:31:01,919 - INFO - 准备合并 3 个场景文件，总时长约 5.16秒
2025-07-28 20:31:01,919 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/89.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/90.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/93.mp4'

2025-07-28 20:31:01,919 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp69_uuwfd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp69_uuwfd\temp_combined.mp4
2025-07-28 20:31:02,085 - INFO - 合并后的视频时长: 5.23秒，目标音频时长: 3.64秒
2025-07-28 20:31:02,085 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp69_uuwfd\temp_combined.mp4 -ss 0 -to 3.637 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_5.mp4
2025-07-28 20:31:02,375 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:02,375 - INFO - 目标音频时长: 3.64秒
2025-07-28 20:31:02,375 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:02,375 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:02,375 - INFO - ==========================================
2025-07-28 20:31:02,375 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:02,375 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_5.mp4
2025-07-28 20:31:02,376 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp69_uuwfd
2025-07-28 20:31:02,418 - INFO - 方案 #5 处理完成:
2025-07-28 20:31:02,418 - INFO -   - 音频时长: 3.64秒
2025-07-28 20:31:02,418 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:02,418 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:02,418 - INFO - 
----- 处理字幕 #4 的方案 #6 -----
2025-07-28 20:31:02,419 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_6.mp4
2025-07-28 20:31:02,419 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwqbemr5w
2025-07-28 20:31:02,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\88.mp4 (确认存在: True)
2025-07-28 20:31:02,419 - INFO - 添加场景ID=88，时长=1.28秒，累计时长=1.28秒
2025-07-28 20:31:02,420 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\96.mp4 (确认存在: True)
2025-07-28 20:31:02,420 - INFO - 添加场景ID=96，时长=1.00秒，累计时长=2.28秒
2025-07-28 20:31:02,420 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\97.mp4 (确认存在: True)
2025-07-28 20:31:02,420 - INFO - 添加场景ID=97，时长=1.36秒，累计时长=3.64秒
2025-07-28 20:31:02,420 - INFO - 准备合并 3 个场景文件，总时长约 3.64秒
2025-07-28 20:31:02,420 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/88.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/96.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/97.mp4'

2025-07-28 20:31:02,420 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwqbemr5w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwqbemr5w\temp_combined.mp4
2025-07-28 20:31:02,595 - INFO - 合并后的视频时长: 3.71秒，目标音频时长: 3.64秒
2025-07-28 20:31:02,595 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwqbemr5w\temp_combined.mp4 -ss 0 -to 3.637 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_6.mp4
2025-07-28 20:31:02,883 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:02,883 - INFO - 目标音频时长: 3.64秒
2025-07-28 20:31:02,883 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:02,883 - INFO - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:02,883 - INFO - ==========================================
2025-07-28 20:31:02,883 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:02,883 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_6.mp4
2025-07-28 20:31:02,883 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwqbemr5w
2025-07-28 20:31:02,925 - INFO - 方案 #6 处理完成:
2025-07-28 20:31:02,925 - INFO -   - 音频时长: 3.64秒
2025-07-28 20:31:02,925 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:02,925 - INFO -   - 时长差异: 0.03秒 (0.71%)
2025-07-28 20:31:02,925 - INFO - 
字幕 #4 处理完成，成功生成 6/6 套方案
2025-07-28 20:31:02,925 - INFO - 生成的视频文件:
2025-07-28 20:31:02,925 - INFO -   1. F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-07-28 20:31:02,925 - INFO -   2. F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-07-28 20:31:02,925 - INFO -   3. F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-07-28 20:31:02,925 - INFO -   4. F:/github/aicut_auto/newcut_ai\4_4.mp4
2025-07-28 20:31:02,925 - INFO -   5. F:/github/aicut_auto/newcut_ai\4_5.mp4
2025-07-28 20:31:02,925 - INFO -   6. F:/github/aicut_auto/newcut_ai\4_6.mp4
2025-07-28 20:31:02,925 - INFO - ========== 字幕 #4 处理结束 ==========

