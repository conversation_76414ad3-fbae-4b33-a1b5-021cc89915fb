2025-07-28 20:31:14,386 - INFO - ========== 字幕 #19 处理开始 ==========
2025-07-28 20:31:14,386 - INFO - 字幕内容: 女孩立刻将消息告知村民，却遭到了二伯的嘲讽与不屑。
2025-07-28 20:31:14,386 - INFO - 字幕序号: [1677, 1681]
2025-07-28 20:31:14,386 - INFO - 音频文件详情:
2025-07-28 20:31:14,386 - INFO -   - 路径: output\19.wav
2025-07-28 20:31:14,386 - INFO -   - 时长: 2.74秒
2025-07-28 20:31:14,387 - INFO -   - 验证音频时长: 2.74秒
2025-07-28 20:31:14,387 - INFO - 字幕时间戳信息:
2025-07-28 20:31:14,387 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:14,387 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:14,387 - INFO -   - 根据生成的音频时长(2.74秒)已调整字幕时间戳
2025-07-28 20:31:14,387 - INFO - ========== 开始为字幕 #19 生成 6 套场景方案 ==========
2025-07-28 20:31:14,387 - INFO - 开始查找字幕序号 [1677, 1681] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:14,387 - INFO - 找到related_overlap场景: scene_id=1745, 字幕#1677
2025-07-28 20:31:14,387 - INFO - 找到related_overlap场景: scene_id=1747, 字幕#1681
2025-07-28 20:31:14,387 - INFO - 找到related_overlap场景: scene_id=1748, 字幕#1681
2025-07-28 20:31:14,388 - INFO - 找到related_between场景: scene_id=1749, 字幕#1681
2025-07-28 20:31:14,388 - INFO - 字幕 #1677 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:14,388 - INFO - 字幕 #1681 找到 2 个overlap场景, 1 个between场景
2025-07-28 20:31:14,388 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 20:31:14,388 - INFO - 开始生成方案 #1
2025-07-28 20:31:14,388 - INFO - 方案 #1: 为字幕#1677选择初始化overlap场景id=1745
2025-07-28 20:31:14,388 - INFO - 方案 #1: 为字幕#1681选择初始化overlap场景id=1748
2025-07-28 20:31:14,388 - INFO - 方案 #1: 初始选择后，当前总时长=2.40秒
2025-07-28 20:31:14,388 - INFO - 方案 #1: 额外添加overlap场景id=1747, 当前总时长=4.08秒
2025-07-28 20:31:14,388 - INFO - 方案 #1: 额外between选择后，当前总时长=4.08秒
2025-07-28 20:31:14,388 - INFO - 方案 #1: 场景总时长(4.08秒)大于音频时长(2.74秒)，需要裁剪
2025-07-28 20:31:14,388 - INFO - 调整前总时长: 4.08秒, 目标时长: 2.74秒
2025-07-28 20:31:14,389 - INFO - 需要裁剪 1.34秒
2025-07-28 20:31:14,389 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:14,389 - INFO - 裁剪场景ID=1747：从1.68秒裁剪至1.00秒
2025-07-28 20:31:14,389 - INFO - 裁剪场景ID=1745：从1.64秒裁剪至1.00秒
2025-07-28 20:31:14,389 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.02秒
2025-07-28 20:31:14,389 - INFO - 移除场景ID=1748，时长=0.76秒
2025-07-28 20:31:14,389 - INFO - 调整后总时长: 2.00秒，与目标时长差异: 0.74秒
2025-07-28 20:31:14,389 - INFO - 方案 #1 调整/填充后最终总时长: 2.00秒
2025-07-28 20:31:14,389 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:14,389 - INFO - 开始生成方案 #2
2025-07-28 20:31:14,389 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:14,389 - INFO - 方案 #2: 为字幕#1681选择初始化between场景id=1749
2025-07-28 20:31:14,389 - INFO - 方案 #2: 额外between选择后，当前总时长=2.24秒
2025-07-28 20:31:14,389 - INFO - 方案 #2: 场景总时长(2.24秒)小于音频时长(2.74秒)，需要延伸填充
2025-07-28 20:31:14,389 - INFO - 方案 #2: 最后一个场景ID: 1749
2025-07-28 20:31:14,389 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 1748
2025-07-28 20:31:14,389 - INFO - 方案 #2: 需要填充时长: 0.50秒
2025-07-28 20:31:14,389 - INFO - 方案 #2: 追加场景 scene_id=1750 (裁剪至 0.50秒)
2025-07-28 20:31:14,389 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:14,389 - INFO - 方案 #2 调整/填充后最终总时长: 2.74秒
2025-07-28 20:31:14,389 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:14,389 - INFO - 开始生成方案 #3
2025-07-28 20:31:14,389 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:14,389 - INFO - 开始生成方案 #4
2025-07-28 20:31:14,389 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:14,389 - INFO - 开始生成方案 #5
2025-07-28 20:31:14,389 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:14,389 - INFO - 开始生成方案 #6
2025-07-28 20:31:14,389 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:14,389 - INFO - ========== 字幕 #19 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:14,389 - INFO - 
----- 处理字幕 #19 的方案 #1 -----
2025-07-28 20:31:14,389 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-28 20:31:14,390 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4rhsb3mi
2025-07-28 20:31:14,390 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1745.mp4 (确认存在: True)
2025-07-28 20:31:14,390 - INFO - 添加场景ID=1745，时长=1.64秒，累计时长=1.64秒
2025-07-28 20:31:14,390 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1747.mp4 (确认存在: True)
2025-07-28 20:31:14,390 - INFO - 添加场景ID=1747，时长=1.68秒，累计时长=3.32秒
2025-07-28 20:31:14,390 - INFO - 准备合并 2 个场景文件，总时长约 3.32秒
2025-07-28 20:31:14,390 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1745.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1747.mp4'

2025-07-28 20:31:14,390 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp4rhsb3mi\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp4rhsb3mi\temp_combined.mp4
2025-07-28 20:31:14,510 - INFO - 合并后的视频时长: 3.37秒，目标音频时长: 2.74秒
2025-07-28 20:31:14,510 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp4rhsb3mi\temp_combined.mp4 -ss 0 -to 2.74 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-28 20:31:14,759 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:14,759 - INFO - 目标音频时长: 2.74秒
2025-07-28 20:31:14,759 - INFO - 实际视频时长: 2.78秒
2025-07-28 20:31:14,759 - INFO - 时长差异: 0.04秒 (1.57%)
2025-07-28 20:31:14,759 - INFO - ==========================================
2025-07-28 20:31:14,759 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:14,759 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-28 20:31:14,760 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4rhsb3mi
2025-07-28 20:31:14,802 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:14,802 - INFO -   - 音频时长: 2.74秒
2025-07-28 20:31:14,802 - INFO -   - 视频时长: 2.78秒
2025-07-28 20:31:14,802 - INFO -   - 时长差异: 0.04秒 (1.57%)
2025-07-28 20:31:14,802 - INFO - 
----- 处理字幕 #19 的方案 #2 -----
2025-07-28 20:31:14,802 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-28 20:31:14,802 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxz5ubkmx
2025-07-28 20:31:14,803 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1749.mp4 (确认存在: True)
2025-07-28 20:31:14,803 - INFO - 添加场景ID=1749，时长=2.24秒，累计时长=2.24秒
2025-07-28 20:31:14,803 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1750.mp4 (确认存在: True)
2025-07-28 20:31:14,803 - INFO - 添加场景ID=1750，时长=3.48秒，累计时长=5.72秒
2025-07-28 20:31:14,803 - INFO - 场景总时长(5.72秒)已达到音频时长(2.74秒)的1.5倍，停止添加场景
2025-07-28 20:31:14,803 - INFO - 准备合并 2 个场景文件，总时长约 5.72秒
2025-07-28 20:31:14,803 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1749.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1750.mp4'

2025-07-28 20:31:14,803 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxz5ubkmx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxz5ubkmx\temp_combined.mp4
2025-07-28 20:31:14,947 - INFO - 合并后的视频时长: 5.77秒，目标音频时长: 2.74秒
2025-07-28 20:31:14,947 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxz5ubkmx\temp_combined.mp4 -ss 0 -to 2.74 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-28 20:31:15,199 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:15,199 - INFO - 目标音频时长: 2.74秒
2025-07-28 20:31:15,199 - INFO - 实际视频时长: 2.78秒
2025-07-28 20:31:15,199 - INFO - 时长差异: 0.04秒 (1.57%)
2025-07-28 20:31:15,199 - INFO - ==========================================
2025-07-28 20:31:15,199 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:15,199 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-28 20:31:15,200 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxz5ubkmx
2025-07-28 20:31:15,241 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:15,241 - INFO -   - 音频时长: 2.74秒
2025-07-28 20:31:15,241 - INFO -   - 视频时长: 2.78秒
2025-07-28 20:31:15,241 - INFO -   - 时长差异: 0.04秒 (1.57%)
2025-07-28 20:31:15,242 - INFO - 
字幕 #19 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:15,242 - INFO - 生成的视频文件:
2025-07-28 20:31:15,242 - INFO -   1. F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-07-28 20:31:15,242 - INFO -   2. F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-07-28 20:31:15,242 - INFO - ========== 字幕 #19 处理结束 ==========

