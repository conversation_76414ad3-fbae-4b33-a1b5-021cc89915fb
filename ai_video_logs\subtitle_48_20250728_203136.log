2025-07-28 20:31:36,935 - INFO - ========== 字幕 #48 处理开始 ==========
2025-07-28 20:31:36,935 - INFO - 字幕内容: 男人举起酒杯，感谢所有人的付出，也为这个家的未来注入了新的希望。
2025-07-28 20:31:36,935 - INFO - 字幕序号: [3368, 3371]
2025-07-28 20:31:36,935 - INFO - 音频文件详情:
2025-07-28 20:31:36,935 - INFO -   - 路径: output\48.wav
2025-07-28 20:31:36,935 - INFO -   - 时长: 3.94秒
2025-07-28 20:31:36,935 - INFO -   - 验证音频时长: 3.94秒
2025-07-28 20:31:36,935 - INFO - 字幕时间戳信息:
2025-07-28 20:31:36,936 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:36,936 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:36,936 - INFO -   - 根据生成的音频时长(3.94秒)已调整字幕时间戳
2025-07-28 20:31:36,936 - INFO - ========== 开始为字幕 #48 生成 6 套场景方案 ==========
2025-07-28 20:31:36,936 - INFO - 开始查找字幕序号 [3368, 3371] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:36,936 - INFO - 找到related_overlap场景: scene_id=3540, 字幕#3368
2025-07-28 20:31:36,936 - INFO - 找到related_overlap场景: scene_id=3541, 字幕#3368
2025-07-28 20:31:36,936 - INFO - 找到related_overlap场景: scene_id=3543, 字幕#3371
2025-07-28 20:31:36,936 - INFO - 找到related_overlap场景: scene_id=3544, 字幕#3371
2025-07-28 20:31:36,937 - INFO - 字幕 #3368 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:36,937 - INFO - 字幕 #3371 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:36,937 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:36,937 - INFO - 开始生成方案 #1
2025-07-28 20:31:36,937 - INFO - 方案 #1: 为字幕#3368选择初始化overlap场景id=3540
2025-07-28 20:31:36,937 - INFO - 方案 #1: 为字幕#3371选择初始化overlap场景id=3544
2025-07-28 20:31:36,937 - INFO - 方案 #1: 初始选择后，当前总时长=2.92秒
2025-07-28 20:31:36,937 - INFO - 方案 #1: 额外添加overlap场景id=3541, 当前总时长=4.52秒
2025-07-28 20:31:36,937 - INFO - 方案 #1: 额外between选择后，当前总时长=4.52秒
2025-07-28 20:31:36,937 - INFO - 方案 #1: 场景总时长(4.52秒)大于音频时长(3.94秒)，需要裁剪
2025-07-28 20:31:36,937 - INFO - 调整前总时长: 4.52秒, 目标时长: 3.94秒
2025-07-28 20:31:36,937 - INFO - 需要裁剪 0.58秒
2025-07-28 20:31:36,937 - INFO - 裁剪最长场景ID=3541：从1.60秒裁剪至1.02秒
2025-07-28 20:31:36,937 - INFO - 调整后总时长: 3.94秒，与目标时长差异: 0.00秒
2025-07-28 20:31:36,937 - INFO - 方案 #1 调整/填充后最终总时长: 3.94秒
2025-07-28 20:31:36,937 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:36,937 - INFO - 开始生成方案 #2
2025-07-28 20:31:36,937 - INFO - 方案 #2: 为字幕#3371选择初始化overlap场景id=3543
2025-07-28 20:31:36,937 - INFO - 方案 #2: 初始选择后，当前总时长=1.32秒
2025-07-28 20:31:36,937 - INFO - 方案 #2: 额外between选择后，当前总时长=1.32秒
2025-07-28 20:31:36,938 - INFO - 方案 #2: 场景总时长(1.32秒)小于音频时长(3.94秒)，需要延伸填充
2025-07-28 20:31:36,938 - INFO - 方案 #2: 最后一个场景ID: 3543
2025-07-28 20:31:36,938 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 3542
2025-07-28 20:31:36,938 - INFO - 方案 #2: 需要填充时长: 2.62秒
2025-07-28 20:31:36,938 - INFO - 方案 #2: 跳过已使用的场景: scene_id=3544
2025-07-28 20:31:36,938 - INFO - 方案 #2: 追加场景 scene_id=3545 (完整时长 1.48秒)
2025-07-28 20:31:36,938 - INFO - 方案 #2: 追加场景 scene_id=3546 (完整时长 0.52秒)
2025-07-28 20:31:36,938 - INFO - 方案 #2: 追加场景 scene_id=3547 (裁剪至 0.62秒)
2025-07-28 20:31:36,938 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:36,938 - INFO - 方案 #2 调整/填充后最终总时长: 3.94秒
2025-07-28 20:31:36,938 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:36,938 - INFO - 开始生成方案 #3
2025-07-28 20:31:36,938 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,938 - INFO - 开始生成方案 #4
2025-07-28 20:31:36,938 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,938 - INFO - 开始生成方案 #5
2025-07-28 20:31:36,938 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,938 - INFO - 开始生成方案 #6
2025-07-28 20:31:36,938 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,938 - INFO - ========== 字幕 #48 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:36,938 - INFO - 
----- 处理字幕 #48 的方案 #1 -----
2025-07-28 20:31:36,938 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-28 20:31:36,938 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgu44lceo
2025-07-28 20:31:36,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3540.mp4 (确认存在: True)
2025-07-28 20:31:36,939 - INFO - 添加场景ID=3540，时长=1.52秒，累计时长=1.52秒
2025-07-28 20:31:36,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3544.mp4 (确认存在: True)
2025-07-28 20:31:36,939 - INFO - 添加场景ID=3544，时长=1.40秒，累计时长=2.92秒
2025-07-28 20:31:36,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3541.mp4 (确认存在: True)
2025-07-28 20:31:36,939 - INFO - 添加场景ID=3541，时长=1.60秒，累计时长=4.52秒
2025-07-28 20:31:36,939 - INFO - 准备合并 3 个场景文件，总时长约 4.52秒
2025-07-28 20:31:36,939 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3540.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3544.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3541.mp4'

2025-07-28 20:31:36,939 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgu44lceo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgu44lceo\temp_combined.mp4
2025-07-28 20:31:37,099 - INFO - 合并后的视频时长: 4.59秒，目标音频时长: 3.94秒
2025-07-28 20:31:37,099 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgu44lceo\temp_combined.mp4 -ss 0 -to 3.94 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-28 20:31:37,386 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:37,387 - INFO - 目标音频时长: 3.94秒
2025-07-28 20:31:37,387 - INFO - 实际视频时长: 3.98秒
2025-07-28 20:31:37,387 - INFO - 时长差异: 0.04秒 (1.09%)
2025-07-28 20:31:37,387 - INFO - ==========================================
2025-07-28 20:31:37,387 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:37,387 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-28 20:31:37,387 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgu44lceo
2025-07-28 20:31:37,428 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:37,428 - INFO -   - 音频时长: 3.94秒
2025-07-28 20:31:37,428 - INFO -   - 视频时长: 3.98秒
2025-07-28 20:31:37,428 - INFO -   - 时长差异: 0.04秒 (1.09%)
2025-07-28 20:31:37,428 - INFO - 
----- 处理字幕 #48 的方案 #2 -----
2025-07-28 20:31:37,428 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-28 20:31:37,428 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdjfxmzaq
2025-07-28 20:31:37,429 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3543.mp4 (确认存在: True)
2025-07-28 20:31:37,429 - INFO - 添加场景ID=3543，时长=1.32秒，累计时长=1.32秒
2025-07-28 20:31:37,429 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3545.mp4 (确认存在: True)
2025-07-28 20:31:37,429 - INFO - 添加场景ID=3545，时长=1.48秒，累计时长=2.80秒
2025-07-28 20:31:37,429 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3546.mp4 (确认存在: True)
2025-07-28 20:31:37,429 - INFO - 添加场景ID=3546，时长=0.52秒，累计时长=3.32秒
2025-07-28 20:31:37,429 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3547.mp4 (确认存在: True)
2025-07-28 20:31:37,429 - INFO - 添加场景ID=3547，时长=0.68秒，累计时长=4.00秒
2025-07-28 20:31:37,429 - INFO - 准备合并 4 个场景文件，总时长约 4.00秒
2025-07-28 20:31:37,429 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3543.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3545.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3546.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3547.mp4'

2025-07-28 20:31:37,429 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdjfxmzaq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdjfxmzaq\temp_combined.mp4
2025-07-28 20:31:37,586 - INFO - 合并后的视频时长: 4.09秒，目标音频时长: 3.94秒
2025-07-28 20:31:37,586 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdjfxmzaq\temp_combined.mp4 -ss 0 -to 3.94 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-28 20:31:37,890 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:37,890 - INFO - 目标音频时长: 3.94秒
2025-07-28 20:31:37,890 - INFO - 实际视频时长: 3.98秒
2025-07-28 20:31:37,890 - INFO - 时长差异: 0.04秒 (1.09%)
2025-07-28 20:31:37,890 - INFO - ==========================================
2025-07-28 20:31:37,890 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:37,890 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-28 20:31:37,891 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdjfxmzaq
2025-07-28 20:31:37,933 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:37,933 - INFO -   - 音频时长: 3.94秒
2025-07-28 20:31:37,933 - INFO -   - 视频时长: 3.98秒
2025-07-28 20:31:37,933 - INFO -   - 时长差异: 0.04秒 (1.09%)
2025-07-28 20:31:37,933 - INFO - 
字幕 #48 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:37,933 - INFO - 生成的视频文件:
2025-07-28 20:31:37,933 - INFO -   1. F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-07-28 20:31:37,933 - INFO -   2. F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-07-28 20:31:37,935 - INFO - ========== 字幕 #48 处理结束 ==========

