2025-07-28 20:31:35,967 - INFO - ========== 字幕 #46 处理开始 ==========
2025-07-28 20:31:35,967 - INFO - 字幕内容: 得知国家有难，女孩的侠义之心被点燃，她当即答应了太子的请求。
2025-07-28 20:31:35,967 - INFO - 字幕序号: [3325, 3328]
2025-07-28 20:31:35,968 - INFO - 音频文件详情:
2025-07-28 20:31:35,968 - INFO -   - 路径: output\46.wav
2025-07-28 20:31:35,968 - INFO -   - 时长: 3.54秒
2025-07-28 20:31:35,968 - INFO -   - 验证音频时长: 3.54秒
2025-07-28 20:31:35,968 - INFO - 字幕时间戳信息:
2025-07-28 20:31:35,968 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:35,968 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:35,968 - INFO -   - 根据生成的音频时长(3.54秒)已调整字幕时间戳
2025-07-28 20:31:35,968 - INFO - ========== 开始为字幕 #46 生成 6 套场景方案 ==========
2025-07-28 20:31:35,968 - INFO - 开始查找字幕序号 [3325, 3328] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:35,969 - INFO - 找到related_overlap场景: scene_id=3495, 字幕#3325
2025-07-28 20:31:35,969 - INFO - 找到related_overlap场景: scene_id=3499, 字幕#3328
2025-07-28 20:31:35,970 - INFO - 找到related_between场景: scene_id=3500, 字幕#3328
2025-07-28 20:31:35,970 - INFO - 字幕 #3325 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:35,970 - INFO - 字幕 #3328 找到 1 个overlap场景, 1 个between场景
2025-07-28 20:31:35,970 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 20:31:35,970 - INFO - 开始生成方案 #1
2025-07-28 20:31:35,970 - INFO - 方案 #1: 为字幕#3325选择初始化overlap场景id=3495
2025-07-28 20:31:35,970 - INFO - 方案 #1: 为字幕#3328选择初始化overlap场景id=3499
2025-07-28 20:31:35,970 - INFO - 方案 #1: 初始选择后，当前总时长=3.28秒
2025-07-28 20:31:35,970 - INFO - 方案 #1: 额外between选择后，当前总时长=3.28秒
2025-07-28 20:31:35,970 - INFO - 方案 #1: 额外添加between场景id=3500, 当前总时长=4.28秒
2025-07-28 20:31:35,970 - INFO - 方案 #1: 场景总时长(4.28秒)大于音频时长(3.54秒)，需要裁剪
2025-07-28 20:31:35,970 - INFO - 调整前总时长: 4.28秒, 目标时长: 3.54秒
2025-07-28 20:31:35,970 - INFO - 需要裁剪 0.74秒
2025-07-28 20:31:35,970 - INFO - 裁剪最长场景ID=3495：从1.80秒裁剪至1.06秒
2025-07-28 20:31:35,970 - INFO - 调整后总时长: 3.54秒，与目标时长差异: 0.00秒
2025-07-28 20:31:35,970 - INFO - 方案 #1 调整/填充后最终总时长: 3.54秒
2025-07-28 20:31:35,970 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:35,970 - INFO - 开始生成方案 #2
2025-07-28 20:31:35,970 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,970 - INFO - 开始生成方案 #3
2025-07-28 20:31:35,970 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,970 - INFO - 开始生成方案 #4
2025-07-28 20:31:35,970 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,970 - INFO - 开始生成方案 #5
2025-07-28 20:31:35,970 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,970 - INFO - 开始生成方案 #6
2025-07-28 20:31:35,970 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,970 - INFO - ========== 字幕 #46 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:35,970 - INFO - 
----- 处理字幕 #46 的方案 #1 -----
2025-07-28 20:31:35,970 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-28 20:31:35,970 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyoro26de
2025-07-28 20:31:35,971 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3495.mp4 (确认存在: True)
2025-07-28 20:31:35,971 - INFO - 添加场景ID=3495，时长=1.80秒，累计时长=1.80秒
2025-07-28 20:31:35,971 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3499.mp4 (确认存在: True)
2025-07-28 20:31:35,971 - INFO - 添加场景ID=3499，时长=1.48秒，累计时长=3.28秒
2025-07-28 20:31:35,971 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3500.mp4 (确认存在: True)
2025-07-28 20:31:35,971 - INFO - 添加场景ID=3500，时长=1.00秒，累计时长=4.28秒
2025-07-28 20:31:35,971 - INFO - 准备合并 3 个场景文件，总时长约 4.28秒
2025-07-28 20:31:35,971 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3495.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3499.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3500.mp4'

2025-07-28 20:31:35,971 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyoro26de\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyoro26de\temp_combined.mp4
2025-07-28 20:31:36,127 - INFO - 合并后的视频时长: 4.35秒，目标音频时长: 3.54秒
2025-07-28 20:31:36,127 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyoro26de\temp_combined.mp4 -ss 0 -to 3.539 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-28 20:31:36,416 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:36,416 - INFO - 目标音频时长: 3.54秒
2025-07-28 20:31:36,416 - INFO - 实际视频时长: 3.58秒
2025-07-28 20:31:36,416 - INFO - 时长差异: 0.04秒 (1.24%)
2025-07-28 20:31:36,416 - INFO - ==========================================
2025-07-28 20:31:36,416 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:36,416 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-28 20:31:36,417 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyoro26de
2025-07-28 20:31:36,460 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:36,460 - INFO -   - 音频时长: 3.54秒
2025-07-28 20:31:36,460 - INFO -   - 视频时长: 3.58秒
2025-07-28 20:31:36,460 - INFO -   - 时长差异: 0.04秒 (1.24%)
2025-07-28 20:31:36,460 - INFO - 
字幕 #46 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:36,460 - INFO - 生成的视频文件:
2025-07-28 20:31:36,460 - INFO -   1. F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-07-28 20:31:36,460 - INFO - ========== 字幕 #46 处理结束 ==========

