2025-07-28 20:31:33,222 - INFO - ========== 字幕 #42 处理开始 ==========
2025-07-28 20:31:33,222 - INFO - 字幕内容: 太子承诺，女孩的所有家人都可以随她一同前往京城，享受荣华。
2025-07-28 20:31:33,222 - INFO - 字幕序号: [3291, 3293]
2025-07-28 20:31:33,222 - INFO - 音频文件详情:
2025-07-28 20:31:33,222 - INFO -   - 路径: output\42.wav
2025-07-28 20:31:33,222 - INFO -   - 时长: 3.85秒
2025-07-28 20:31:33,223 - INFO -   - 验证音频时长: 3.85秒
2025-07-28 20:31:33,223 - INFO - 字幕时间戳信息:
2025-07-28 20:31:33,223 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:33,223 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:33,223 - INFO -   - 根据生成的音频时长(3.85秒)已调整字幕时间戳
2025-07-28 20:31:33,223 - INFO - ========== 开始为字幕 #42 生成 6 套场景方案 ==========
2025-07-28 20:31:33,223 - INFO - 开始查找字幕序号 [3291, 3293] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:33,224 - INFO - 找到related_overlap场景: scene_id=3453, 字幕#3291
2025-07-28 20:31:33,224 - INFO - 找到related_overlap场景: scene_id=3456, 字幕#3293
2025-07-28 20:31:33,224 - INFO - 找到related_between场景: scene_id=3457, 字幕#3293
2025-07-28 20:31:33,224 - INFO - 找到related_between场景: scene_id=3458, 字幕#3293
2025-07-28 20:31:33,224 - INFO - 字幕 #3291 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:33,224 - INFO - 字幕 #3293 找到 1 个overlap场景, 2 个between场景
2025-07-28 20:31:33,224 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-07-28 20:31:33,224 - INFO - 开始生成方案 #1
2025-07-28 20:31:33,224 - INFO - 方案 #1: 为字幕#3291选择初始化overlap场景id=3453
2025-07-28 20:31:33,224 - INFO - 方案 #1: 为字幕#3293选择初始化overlap场景id=3456
2025-07-28 20:31:33,224 - INFO - 方案 #1: 初始选择后，当前总时长=3.20秒
2025-07-28 20:31:33,224 - INFO - 方案 #1: 额外between选择后，当前总时长=3.20秒
2025-07-28 20:31:33,224 - INFO - 方案 #1: 额外添加between场景id=3457, 当前总时长=4.36秒
2025-07-28 20:31:33,224 - INFO - 方案 #1: 场景总时长(4.36秒)大于音频时长(3.85秒)，需要裁剪
2025-07-28 20:31:33,224 - INFO - 调整前总时长: 4.36秒, 目标时长: 3.85秒
2025-07-28 20:31:33,224 - INFO - 需要裁剪 0.51秒
2025-07-28 20:31:33,224 - INFO - 裁剪最长场景ID=3453：从2.16秒裁剪至1.65秒
2025-07-28 20:31:33,224 - INFO - 调整后总时长: 3.85秒，与目标时长差异: 0.00秒
2025-07-28 20:31:33,224 - INFO - 方案 #1 调整/填充后最终总时长: 3.85秒
2025-07-28 20:31:33,224 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:33,224 - INFO - 开始生成方案 #2
2025-07-28 20:31:33,224 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:33,224 - INFO - 方案 #2: 为字幕#3293选择初始化between场景id=3458
2025-07-28 20:31:33,224 - INFO - 方案 #2: 额外between选择后，当前总时长=1.28秒
2025-07-28 20:31:33,224 - INFO - 方案 #2: 场景总时长(1.28秒)小于音频时长(3.85秒)，需要延伸填充
2025-07-28 20:31:33,224 - INFO - 方案 #2: 最后一个场景ID: 3458
2025-07-28 20:31:33,225 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 3457
2025-07-28 20:31:33,225 - INFO - 方案 #2: 需要填充时长: 2.57秒
2025-07-28 20:31:33,225 - INFO - 方案 #2: 追加场景 scene_id=3459 (完整时长 1.44秒)
2025-07-28 20:31:33,225 - INFO - 方案 #2: 追加场景 scene_id=3460 (裁剪至 1.13秒)
2025-07-28 20:31:33,225 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:33,225 - INFO - 方案 #2 调整/填充后最终总时长: 3.85秒
2025-07-28 20:31:33,225 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:33,225 - INFO - 开始生成方案 #3
2025-07-28 20:31:33,225 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:33,225 - INFO - 开始生成方案 #4
2025-07-28 20:31:33,225 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:33,225 - INFO - 开始生成方案 #5
2025-07-28 20:31:33,225 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:33,225 - INFO - 开始生成方案 #6
2025-07-28 20:31:33,225 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:33,225 - INFO - ========== 字幕 #42 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:33,225 - INFO - 
----- 处理字幕 #42 的方案 #1 -----
2025-07-28 20:31:33,225 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-28 20:31:33,225 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe318etbr
2025-07-28 20:31:33,226 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3453.mp4 (确认存在: True)
2025-07-28 20:31:33,226 - INFO - 添加场景ID=3453，时长=2.16秒，累计时长=2.16秒
2025-07-28 20:31:33,226 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3456.mp4 (确认存在: True)
2025-07-28 20:31:33,226 - INFO - 添加场景ID=3456，时长=1.04秒，累计时长=3.20秒
2025-07-28 20:31:33,226 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3457.mp4 (确认存在: True)
2025-07-28 20:31:33,226 - INFO - 添加场景ID=3457，时长=1.16秒，累计时长=4.36秒
2025-07-28 20:31:33,226 - INFO - 准备合并 3 个场景文件，总时长约 4.36秒
2025-07-28 20:31:33,226 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3453.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3456.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3457.mp4'

2025-07-28 20:31:33,226 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe318etbr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe318etbr\temp_combined.mp4
2025-07-28 20:31:33,387 - INFO - 合并后的视频时长: 4.43秒，目标音频时长: 3.85秒
2025-07-28 20:31:33,387 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe318etbr\temp_combined.mp4 -ss 0 -to 3.851 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-28 20:31:33,674 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:33,674 - INFO - 目标音频时长: 3.85秒
2025-07-28 20:31:33,674 - INFO - 实际视频时长: 3.90秒
2025-07-28 20:31:33,674 - INFO - 时长差异: 0.05秒 (1.35%)
2025-07-28 20:31:33,674 - INFO - ==========================================
2025-07-28 20:31:33,674 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:33,674 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-28 20:31:33,675 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe318etbr
2025-07-28 20:31:33,716 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:33,716 - INFO -   - 音频时长: 3.85秒
2025-07-28 20:31:33,716 - INFO -   - 视频时长: 3.90秒
2025-07-28 20:31:33,716 - INFO -   - 时长差异: 0.05秒 (1.35%)
2025-07-28 20:31:33,716 - INFO - 
----- 处理字幕 #42 的方案 #2 -----
2025-07-28 20:31:33,716 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-28 20:31:33,716 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpygykggku
2025-07-28 20:31:33,717 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3458.mp4 (确认存在: True)
2025-07-28 20:31:33,717 - INFO - 添加场景ID=3458，时长=1.28秒，累计时长=1.28秒
2025-07-28 20:31:33,717 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3459.mp4 (确认存在: True)
2025-07-28 20:31:33,717 - INFO - 添加场景ID=3459，时长=1.44秒，累计时长=2.72秒
2025-07-28 20:31:33,717 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3460.mp4 (确认存在: True)
2025-07-28 20:31:33,717 - INFO - 添加场景ID=3460，时长=1.52秒，累计时长=4.24秒
2025-07-28 20:31:33,717 - INFO - 准备合并 3 个场景文件，总时长约 4.24秒
2025-07-28 20:31:33,717 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3458.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3459.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3460.mp4'

2025-07-28 20:31:33,717 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpygykggku\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpygykggku\temp_combined.mp4
2025-07-28 20:31:33,892 - INFO - 合并后的视频时长: 4.31秒，目标音频时长: 3.85秒
2025-07-28 20:31:33,892 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpygykggku\temp_combined.mp4 -ss 0 -to 3.851 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-28 20:31:34,195 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:34,196 - INFO - 目标音频时长: 3.85秒
2025-07-28 20:31:34,196 - INFO - 实际视频时长: 3.90秒
2025-07-28 20:31:34,196 - INFO - 时长差异: 0.05秒 (1.35%)
2025-07-28 20:31:34,196 - INFO - ==========================================
2025-07-28 20:31:34,196 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:34,196 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-28 20:31:34,196 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpygykggku
2025-07-28 20:31:34,239 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:34,239 - INFO -   - 音频时长: 3.85秒
2025-07-28 20:31:34,239 - INFO -   - 视频时长: 3.90秒
2025-07-28 20:31:34,239 - INFO -   - 时长差异: 0.05秒 (1.35%)
2025-07-28 20:31:34,239 - INFO - 
字幕 #42 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:34,239 - INFO - 生成的视频文件:
2025-07-28 20:31:34,239 - INFO -   1. F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-07-28 20:31:34,239 - INFO -   2. F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-07-28 20:31:34,239 - INFO - ========== 字幕 #42 处理结束 ==========

