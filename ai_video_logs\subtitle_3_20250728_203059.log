2025-07-28 20:30:59,553 - INFO - ========== 字幕 #3 处理开始 ==========
2025-07-28 20:30:59,553 - INFO - 字幕内容: 贪婪的二伯娘不仅不帮忙，反而趁火打劫，要抢走男人最后的救命钱。
2025-07-28 20:30:59,553 - INFO - 字幕序号: [73, 74]
2025-07-28 20:30:59,553 - INFO - 音频文件详情:
2025-07-28 20:30:59,553 - INFO -   - 路径: output\3.wav
2025-07-28 20:30:59,553 - INFO -   - 时长: 4.23秒
2025-07-28 20:30:59,553 - INFO -   - 验证音频时长: 4.23秒
2025-07-28 20:30:59,553 - INFO - 字幕时间戳信息:
2025-07-28 20:30:59,553 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:30:59,553 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:30:59,553 - INFO -   - 根据生成的音频时长(4.23秒)已调整字幕时间戳
2025-07-28 20:30:59,553 - INFO - ========== 开始为字幕 #3 生成 6 套场景方案 ==========
2025-07-28 20:30:59,553 - INFO - 开始查找字幕序号 [73, 74] 对应的场景，共有 3562 个场景可选
2025-07-28 20:30:59,554 - INFO - 找到related_overlap场景: scene_id=78, 字幕#73
2025-07-28 20:30:59,554 - INFO - 找到related_overlap场景: scene_id=79, 字幕#74
2025-07-28 20:30:59,554 - INFO - 找到related_between场景: scene_id=80, 字幕#74
2025-07-28 20:30:59,555 - INFO - 字幕 #73 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:30:59,555 - INFO - 字幕 #74 找到 1 个overlap场景, 1 个between场景
2025-07-28 20:30:59,555 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 20:30:59,555 - INFO - 开始生成方案 #1
2025-07-28 20:30:59,555 - INFO - 方案 #1: 为字幕#73选择初始化overlap场景id=78
2025-07-28 20:30:59,555 - INFO - 方案 #1: 为字幕#74选择初始化overlap场景id=79
2025-07-28 20:30:59,555 - INFO - 方案 #1: 初始选择后，当前总时长=2.72秒
2025-07-28 20:30:59,555 - INFO - 方案 #1: 额外between选择后，当前总时长=2.72秒
2025-07-28 20:30:59,555 - INFO - 方案 #1: 额外添加between场景id=80, 当前总时长=4.16秒
2025-07-28 20:30:59,555 - INFO - 方案 #1: 场景总时长(4.16秒)小于音频时长(4.23秒)，需要延伸填充
2025-07-28 20:30:59,555 - INFO - 方案 #1: 最后一个场景ID: 80
2025-07-28 20:30:59,555 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 79
2025-07-28 20:30:59,555 - INFO - 方案 #1: 需要填充时长: 0.07秒
2025-07-28 20:30:59,555 - INFO - 方案 #1: 追加场景 scene_id=81 (裁剪至 0.07秒)
2025-07-28 20:30:59,555 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 20:30:59,555 - INFO - 方案 #1 调整/填充后最终总时长: 4.23秒
2025-07-28 20:30:59,555 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:30:59,555 - INFO - 开始生成方案 #2
2025-07-28 20:30:59,555 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,555 - INFO - 开始生成方案 #3
2025-07-28 20:30:59,555 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,555 - INFO - 开始生成方案 #4
2025-07-28 20:30:59,555 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,555 - INFO - 开始生成方案 #5
2025-07-28 20:30:59,555 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,555 - INFO - 开始生成方案 #6
2025-07-28 20:30:59,555 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:30:59,555 - INFO - ========== 字幕 #3 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:30:59,555 - INFO - 
----- 处理字幕 #3 的方案 #1 -----
2025-07-28 20:30:59,555 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-28 20:30:59,556 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppn3k8oxl
2025-07-28 20:30:59,556 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\78.mp4 (确认存在: True)
2025-07-28 20:30:59,556 - INFO - 添加场景ID=78，时长=1.08秒，累计时长=1.08秒
2025-07-28 20:30:59,556 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\79.mp4 (确认存在: True)
2025-07-28 20:30:59,556 - INFO - 添加场景ID=79，时长=1.64秒，累计时长=2.72秒
2025-07-28 20:30:59,556 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\80.mp4 (确认存在: True)
2025-07-28 20:30:59,556 - INFO - 添加场景ID=80，时长=1.44秒，累计时长=4.16秒
2025-07-28 20:30:59,556 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\81.mp4 (确认存在: True)
2025-07-28 20:30:59,556 - INFO - 添加场景ID=81，时长=1.76秒，累计时长=5.92秒
2025-07-28 20:30:59,556 - INFO - 准备合并 4 个场景文件，总时长约 5.92秒
2025-07-28 20:30:59,557 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/78.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/79.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/80.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/81.mp4'

2025-07-28 20:30:59,557 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppn3k8oxl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppn3k8oxl\temp_combined.mp4
2025-07-28 20:30:59,720 - INFO - 合并后的视频时长: 5.99秒，目标音频时长: 4.23秒
2025-07-28 20:30:59,721 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppn3k8oxl\temp_combined.mp4 -ss 0 -to 4.231 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-28 20:31:00,022 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:00,022 - INFO - 目标音频时长: 4.23秒
2025-07-28 20:31:00,022 - INFO - 实际视频时长: 4.26秒
2025-07-28 20:31:00,022 - INFO - 时长差异: 0.03秒 (0.76%)
2025-07-28 20:31:00,022 - INFO - ==========================================
2025-07-28 20:31:00,022 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:00,022 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-28 20:31:00,023 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppn3k8oxl
2025-07-28 20:31:00,064 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:00,064 - INFO -   - 音频时长: 4.23秒
2025-07-28 20:31:00,064 - INFO -   - 视频时长: 4.26秒
2025-07-28 20:31:00,064 - INFO -   - 时长差异: 0.03秒 (0.76%)
2025-07-28 20:31:00,064 - INFO - 
字幕 #3 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:00,064 - INFO - 生成的视频文件:
2025-07-28 20:31:00,064 - INFO -   1. F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-07-28 20:31:00,064 - INFO - ========== 字幕 #3 处理结束 ==========

