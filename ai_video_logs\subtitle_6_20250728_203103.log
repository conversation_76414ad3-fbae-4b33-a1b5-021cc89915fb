2025-07-28 20:31:03,432 - INFO - ========== 字幕 #6 处理开始 ==========
2025-07-28 20:31:03,434 - INFO - 字幕内容: 下一秒，在众人震惊的目光中，女孩竟将一个成年人轻松举起，挂到了房梁之上。
2025-07-28 20:31:03,434 - INFO - 字幕序号: [196, 197]
2025-07-28 20:31:03,434 - INFO - 音频文件详情:
2025-07-28 20:31:03,434 - INFO -   - 路径: output\6.wav
2025-07-28 20:31:03,434 - INFO -   - 时长: 3.61秒
2025-07-28 20:31:03,434 - INFO -   - 验证音频时长: 3.61秒
2025-07-28 20:31:03,434 - INFO - 字幕时间戳信息:
2025-07-28 20:31:03,434 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:03,434 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:03,434 - INFO -   - 根据生成的音频时长(3.61秒)已调整字幕时间戳
2025-07-28 20:31:03,434 - INFO - ========== 开始为字幕 #6 生成 6 套场景方案 ==========
2025-07-28 20:31:03,434 - INFO - 开始查找字幕序号 [196, 197] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:03,434 - INFO - 找到related_overlap场景: scene_id=234, 字幕#196
2025-07-28 20:31:03,434 - INFO - 找到related_overlap场景: scene_id=238, 字幕#197
2025-07-28 20:31:03,435 - INFO - 找到related_between场景: scene_id=235, 字幕#196
2025-07-28 20:31:03,435 - INFO - 找到related_between场景: scene_id=236, 字幕#196
2025-07-28 20:31:03,435 - INFO - 找到related_between场景: scene_id=237, 字幕#196
2025-07-28 20:31:03,436 - INFO - 字幕 #196 找到 1 个overlap场景, 3 个between场景
2025-07-28 20:31:03,436 - INFO - 字幕 #197 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:03,436 - INFO - 共收集 1 个未使用的overlap场景和 3 个未使用的between场景
2025-07-28 20:31:03,436 - INFO - 开始生成方案 #1
2025-07-28 20:31:03,436 - INFO - 方案 #1: 为字幕#197选择初始化overlap场景id=238
2025-07-28 20:31:03,437 - INFO - 方案 #1: 初始选择后，当前总时长=1.80秒
2025-07-28 20:31:03,437 - INFO - 方案 #1: 为字幕#196选择初始化between场景id=237
2025-07-28 20:31:03,437 - INFO - 方案 #1: 额外between选择后，当前总时长=2.80秒
2025-07-28 20:31:03,437 - INFO - 方案 #1: 额外添加between场景id=235, 当前总时长=3.36秒
2025-07-28 20:31:03,437 - INFO - 方案 #1: 额外添加between场景id=236, 当前总时长=4.28秒
2025-07-28 20:31:03,437 - INFO - 方案 #1: 场景总时长(4.28秒)大于音频时长(3.61秒)，需要裁剪
2025-07-28 20:31:03,437 - INFO - 调整前总时长: 4.28秒, 目标时长: 3.61秒
2025-07-28 20:31:03,437 - INFO - 需要裁剪 0.67秒
2025-07-28 20:31:03,437 - INFO - 裁剪最长场景ID=238：从1.80秒裁剪至1.13秒
2025-07-28 20:31:03,437 - INFO - 调整后总时长: 3.61秒，与目标时长差异: 0.00秒
2025-07-28 20:31:03,437 - INFO - 方案 #1 调整/填充后最终总时长: 3.61秒
2025-07-28 20:31:03,437 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:03,437 - INFO - 开始生成方案 #2
2025-07-28 20:31:03,437 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,437 - INFO - 开始生成方案 #3
2025-07-28 20:31:03,437 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,437 - INFO - 开始生成方案 #4
2025-07-28 20:31:03,437 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,437 - INFO - 开始生成方案 #5
2025-07-28 20:31:03,437 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,437 - INFO - 开始生成方案 #6
2025-07-28 20:31:03,437 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:03,437 - INFO - ========== 字幕 #6 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:03,437 - INFO - 
----- 处理字幕 #6 的方案 #1 -----
2025-07-28 20:31:03,437 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-28 20:31:03,438 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjzp17ugt
2025-07-28 20:31:03,438 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\238.mp4 (确认存在: True)
2025-07-28 20:31:03,438 - INFO - 添加场景ID=238，时长=1.80秒，累计时长=1.80秒
2025-07-28 20:31:03,438 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\237.mp4 (确认存在: True)
2025-07-28 20:31:03,438 - INFO - 添加场景ID=237，时长=1.00秒，累计时长=2.80秒
2025-07-28 20:31:03,439 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\235.mp4 (确认存在: True)
2025-07-28 20:31:03,439 - INFO - 添加场景ID=235，时长=0.56秒，累计时长=3.36秒
2025-07-28 20:31:03,439 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\236.mp4 (确认存在: True)
2025-07-28 20:31:03,439 - INFO - 添加场景ID=236，时长=0.92秒，累计时长=4.28秒
2025-07-28 20:31:03,439 - INFO - 准备合并 4 个场景文件，总时长约 4.28秒
2025-07-28 20:31:03,439 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/238.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/237.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/235.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/236.mp4'

2025-07-28 20:31:03,439 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjzp17ugt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjzp17ugt\temp_combined.mp4
2025-07-28 20:31:03,601 - INFO - 合并后的视频时长: 4.35秒，目标音频时长: 3.61秒
2025-07-28 20:31:03,601 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjzp17ugt\temp_combined.mp4 -ss 0 -to 3.607 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-28 20:31:03,885 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:03,886 - INFO - 目标音频时长: 3.61秒
2025-07-28 20:31:03,886 - INFO - 实际视频时长: 3.66秒
2025-07-28 20:31:03,886 - INFO - 时长差异: 0.06秒 (1.55%)
2025-07-28 20:31:03,886 - INFO - ==========================================
2025-07-28 20:31:03,886 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:03,886 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-28 20:31:03,886 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjzp17ugt
2025-07-28 20:31:03,927 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:03,927 - INFO -   - 音频时长: 3.61秒
2025-07-28 20:31:03,927 - INFO -   - 视频时长: 3.66秒
2025-07-28 20:31:03,927 - INFO -   - 时长差异: 0.06秒 (1.55%)
2025-07-28 20:31:03,927 - INFO - 
字幕 #6 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:03,927 - INFO - 生成的视频文件:
2025-07-28 20:31:03,927 - INFO -   1. F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-07-28 20:31:03,927 - INFO - ========== 字幕 #6 处理结束 ==========

