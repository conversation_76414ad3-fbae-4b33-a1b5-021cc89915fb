2025-07-28 20:31:37,935 - INFO - ========== 字幕 #49 处理开始 ==========
2025-07-28 20:31:37,935 - INFO - 字幕内容: 女孩看着眼前温馨的景象，心中感慨万千，无论为何而来，她都将守护好这份来之不易的幸福。
2025-07-28 20:31:37,935 - INFO - 字幕序号: [3377, 3381]
2025-07-28 20:31:37,935 - INFO - 音频文件详情:
2025-07-28 20:31:37,935 - INFO -   - 路径: output\49.wav
2025-07-28 20:31:37,935 - INFO -   - 时长: 4.47秒
2025-07-28 20:31:37,936 - INFO -   - 验证音频时长: 4.47秒
2025-07-28 20:31:37,936 - INFO - 字幕时间戳信息:
2025-07-28 20:31:37,936 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:37,936 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:37,936 - INFO -   - 根据生成的音频时长(4.47秒)已调整字幕时间戳
2025-07-28 20:31:37,936 - INFO - ========== 开始为字幕 #49 生成 6 套场景方案 ==========
2025-07-28 20:31:37,936 - INFO - 开始查找字幕序号 [3377, 3381] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:37,937 - INFO - 找到related_overlap场景: scene_id=3556, 字幕#3377
2025-07-28 20:31:37,937 - INFO - 找到related_overlap场景: scene_id=3558, 字幕#3377
2025-07-28 20:31:37,937 - INFO - 找到related_overlap场景: scene_id=3561, 字幕#3381
2025-07-28 20:31:37,937 - INFO - 字幕 #3377 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:37,937 - INFO - 字幕 #3381 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:37,937 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:37,937 - INFO - 开始生成方案 #1
2025-07-28 20:31:37,937 - INFO - 方案 #1: 为字幕#3377选择初始化overlap场景id=3556
2025-07-28 20:31:37,937 - INFO - 方案 #1: 为字幕#3381选择初始化overlap场景id=3561
2025-07-28 20:31:37,937 - INFO - 方案 #1: 初始选择后，当前总时长=3.20秒
2025-07-28 20:31:37,937 - INFO - 方案 #1: 额外添加overlap场景id=3558, 当前总时长=4.76秒
2025-07-28 20:31:37,937 - INFO - 方案 #1: 额外between选择后，当前总时长=4.76秒
2025-07-28 20:31:37,937 - INFO - 方案 #1: 场景总时长(4.76秒)大于音频时长(4.47秒)，需要裁剪
2025-07-28 20:31:37,937 - INFO - 调整前总时长: 4.76秒, 目标时长: 4.47秒
2025-07-28 20:31:37,937 - INFO - 需要裁剪 0.29秒
2025-07-28 20:31:37,937 - INFO - 裁剪最长场景ID=3556：从1.88秒裁剪至1.59秒
2025-07-28 20:31:37,937 - INFO - 调整后总时长: 4.47秒，与目标时长差异: 0.00秒
2025-07-28 20:31:37,938 - INFO - 方案 #1 调整/填充后最终总时长: 4.47秒
2025-07-28 20:31:37,938 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:37,938 - INFO - 开始生成方案 #2
2025-07-28 20:31:37,938 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:37,938 - INFO - 开始生成方案 #3
2025-07-28 20:31:37,938 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:37,938 - INFO - 开始生成方案 #4
2025-07-28 20:31:37,938 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:37,938 - INFO - 开始生成方案 #5
2025-07-28 20:31:37,938 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:37,938 - INFO - 开始生成方案 #6
2025-07-28 20:31:37,938 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:37,938 - INFO - ========== 字幕 #49 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:37,938 - INFO - 
----- 处理字幕 #49 的方案 #1 -----
2025-07-28 20:31:37,938 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-28 20:31:37,938 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7vwqfncr
2025-07-28 20:31:37,938 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3556.mp4 (确认存在: True)
2025-07-28 20:31:37,939 - INFO - 添加场景ID=3556，时长=1.88秒，累计时长=1.88秒
2025-07-28 20:31:37,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3561.mp4 (确认存在: True)
2025-07-28 20:31:37,939 - INFO - 添加场景ID=3561，时长=1.32秒，累计时长=3.20秒
2025-07-28 20:31:37,939 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3558.mp4 (确认存在: True)
2025-07-28 20:31:37,939 - INFO - 添加场景ID=3558，时长=1.56秒，累计时长=4.76秒
2025-07-28 20:31:37,939 - INFO - 准备合并 3 个场景文件，总时长约 4.76秒
2025-07-28 20:31:37,939 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3556.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3561.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3558.mp4'

2025-07-28 20:31:37,939 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7vwqfncr\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7vwqfncr\temp_combined.mp4
2025-07-28 20:31:38,089 - INFO - 合并后的视频时长: 4.83秒，目标音频时长: 4.47秒
2025-07-28 20:31:38,089 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7vwqfncr\temp_combined.mp4 -ss 0 -to 4.473 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-28 20:31:38,436 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:38,437 - INFO - 目标音频时长: 4.47秒
2025-07-28 20:31:38,437 - INFO - 实际视频时长: 4.50秒
2025-07-28 20:31:38,437 - INFO - 时长差异: 0.03秒 (0.67%)
2025-07-28 20:31:38,437 - INFO - ==========================================
2025-07-28 20:31:38,437 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:38,437 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-28 20:31:38,437 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7vwqfncr
2025-07-28 20:31:38,484 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:38,484 - INFO -   - 音频时长: 4.47秒
2025-07-28 20:31:38,484 - INFO -   - 视频时长: 4.50秒
2025-07-28 20:31:38,484 - INFO -   - 时长差异: 0.03秒 (0.67%)
2025-07-28 20:31:38,484 - INFO - 
字幕 #49 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:38,484 - INFO - 生成的视频文件:
2025-07-28 20:31:38,484 - INFO -   1. F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-07-28 20:31:38,484 - INFO - ========== 字幕 #49 处理结束 ==========

