2025-07-28 20:31:11,105 - INFO - ========== 字幕 #15 处理开始 ==========
2025-07-28 20:31:11,105 - INFO - 字幕内容: 傍晚，当其他猎人还在为一只小兔子炫耀时，女孩的小弟自豪地展示出她们的收获，堆积如山的猎物惊呆了众人。
2025-07-28 20:31:11,105 - INFO - 字幕序号: [1086, 1090]
2025-07-28 20:31:11,105 - INFO - 音频文件详情:
2025-07-28 20:31:11,105 - INFO -   - 路径: output\15.wav
2025-07-28 20:31:11,105 - INFO -   - 时长: 5.14秒
2025-07-28 20:31:11,105 - INFO -   - 验证音频时长: 5.14秒
2025-07-28 20:31:11,106 - INFO - 字幕时间戳信息:
2025-07-28 20:31:11,106 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:11,106 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:11,106 - INFO -   - 根据生成的音频时长(5.14秒)已调整字幕时间戳
2025-07-28 20:31:11,106 - INFO - ========== 开始为字幕 #15 生成 6 套场景方案 ==========
2025-07-28 20:31:11,106 - INFO - 开始查找字幕序号 [1086, 1090] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:11,106 - INFO - 找到related_overlap场景: scene_id=1160, 字幕#1086
2025-07-28 20:31:11,106 - INFO - 找到related_overlap场景: scene_id=1161, 字幕#1086
2025-07-28 20:31:11,106 - INFO - 找到related_overlap场景: scene_id=1163, 字幕#1090
2025-07-28 20:31:11,107 - INFO - 字幕 #1086 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:11,107 - INFO - 字幕 #1090 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:11,107 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:11,107 - INFO - 开始生成方案 #1
2025-07-28 20:31:11,107 - INFO - 方案 #1: 为字幕#1086选择初始化overlap场景id=1160
2025-07-28 20:31:11,107 - INFO - 方案 #1: 为字幕#1090选择初始化overlap场景id=1163
2025-07-28 20:31:11,107 - INFO - 方案 #1: 初始选择后，当前总时长=2.80秒
2025-07-28 20:31:11,107 - INFO - 方案 #1: 额外添加overlap场景id=1161, 当前总时长=4.68秒
2025-07-28 20:31:11,107 - INFO - 方案 #1: 额外between选择后，当前总时长=4.68秒
2025-07-28 20:31:11,107 - INFO - 方案 #1: 场景总时长(4.68秒)小于音频时长(5.14秒)，需要延伸填充
2025-07-28 20:31:11,107 - INFO - 方案 #1: 最后一个场景ID: 1161
2025-07-28 20:31:11,107 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 1160
2025-07-28 20:31:11,107 - INFO - 方案 #1: 需要填充时长: 0.46秒
2025-07-28 20:31:11,107 - INFO - 方案 #1: 追加场景 scene_id=1162 (裁剪至 0.46秒)
2025-07-28 20:31:11,107 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 20:31:11,107 - INFO - 方案 #1 调整/填充后最终总时长: 5.14秒
2025-07-28 20:31:11,107 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:11,107 - INFO - 开始生成方案 #2
2025-07-28 20:31:11,107 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,107 - INFO - 开始生成方案 #3
2025-07-28 20:31:11,107 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,107 - INFO - 开始生成方案 #4
2025-07-28 20:31:11,107 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,107 - INFO - 开始生成方案 #5
2025-07-28 20:31:11,107 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,107 - INFO - 开始生成方案 #6
2025-07-28 20:31:11,107 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:11,107 - INFO - ========== 字幕 #15 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:11,107 - INFO - 
----- 处理字幕 #15 的方案 #1 -----
2025-07-28 20:31:11,107 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-28 20:31:11,108 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpifiy7mpy
2025-07-28 20:31:11,108 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1160.mp4 (确认存在: True)
2025-07-28 20:31:11,108 - INFO - 添加场景ID=1160，时长=1.48秒，累计时长=1.48秒
2025-07-28 20:31:11,108 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1163.mp4 (确认存在: True)
2025-07-28 20:31:11,108 - INFO - 添加场景ID=1163，时长=1.32秒，累计时长=2.80秒
2025-07-28 20:31:11,108 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1161.mp4 (确认存在: True)
2025-07-28 20:31:11,108 - INFO - 添加场景ID=1161，时长=1.88秒，累计时长=4.68秒
2025-07-28 20:31:11,109 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1162.mp4 (确认存在: True)
2025-07-28 20:31:11,109 - INFO - 添加场景ID=1162，时长=5.84秒，累计时长=10.52秒
2025-07-28 20:31:11,109 - INFO - 场景总时长(10.52秒)已达到音频时长(5.14秒)的1.5倍，停止添加场景
2025-07-28 20:31:11,109 - INFO - 准备合并 4 个场景文件，总时长约 10.52秒
2025-07-28 20:31:11,109 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1160.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1163.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1161.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1162.mp4'

2025-07-28 20:31:11,109 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpifiy7mpy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpifiy7mpy\temp_combined.mp4
2025-07-28 20:31:11,309 - INFO - 合并后的视频时长: 10.61秒，目标音频时长: 5.14秒
2025-07-28 20:31:11,309 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpifiy7mpy\temp_combined.mp4 -ss 0 -to 5.14 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-28 20:31:11,630 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:11,630 - INFO - 目标音频时长: 5.14秒
2025-07-28 20:31:11,630 - INFO - 实际视频时长: 5.18秒
2025-07-28 20:31:11,630 - INFO - 时长差异: 0.04秒 (0.84%)
2025-07-28 20:31:11,630 - INFO - ==========================================
2025-07-28 20:31:11,630 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:11,630 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-28 20:31:11,631 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpifiy7mpy
2025-07-28 20:31:11,671 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:11,671 - INFO -   - 音频时长: 5.14秒
2025-07-28 20:31:11,671 - INFO -   - 视频时长: 5.18秒
2025-07-28 20:31:11,671 - INFO -   - 时长差异: 0.04秒 (0.84%)
2025-07-28 20:31:11,671 - INFO - 
字幕 #15 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:11,671 - INFO - 生成的视频文件:
2025-07-28 20:31:11,671 - INFO -   1. F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-07-28 20:31:11,671 - INFO - ========== 字幕 #15 处理结束 ==========

