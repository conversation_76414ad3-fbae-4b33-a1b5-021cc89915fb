2025-07-28 20:31:36,461 - INFO - ========== 字幕 #47 处理开始 ==========
2025-07-28 20:31:36,461 - INFO - 字幕内容: 数日后，女孩的父亲终于痊愈，一家人围坐在一起，其乐融融。
2025-07-28 20:31:36,461 - INFO - 字幕序号: [3354, 3357]
2025-07-28 20:31:36,461 - INFO - 音频文件详情:
2025-07-28 20:31:36,461 - INFO -   - 路径: output\47.wav
2025-07-28 20:31:36,461 - INFO -   - 时长: 2.94秒
2025-07-28 20:31:36,461 - INFO -   - 验证音频时长: 2.94秒
2025-07-28 20:31:36,461 - INFO - 字幕时间戳信息:
2025-07-28 20:31:36,461 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:36,461 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:36,461 - INFO -   - 根据生成的音频时长(2.94秒)已调整字幕时间戳
2025-07-28 20:31:36,461 - INFO - ========== 开始为字幕 #47 生成 6 套场景方案 ==========
2025-07-28 20:31:36,462 - INFO - 开始查找字幕序号 [3354, 3357] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:36,462 - INFO - 找到related_overlap场景: scene_id=3530, 字幕#3354
2025-07-28 20:31:36,462 - INFO - 找到related_overlap场景: scene_id=3532, 字幕#3357
2025-07-28 20:31:36,463 - INFO - 字幕 #3354 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:36,463 - INFO - 字幕 #3357 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:36,463 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:36,463 - INFO - 开始生成方案 #1
2025-07-28 20:31:36,463 - INFO - 方案 #1: 为字幕#3354选择初始化overlap场景id=3530
2025-07-28 20:31:36,463 - INFO - 方案 #1: 为字幕#3357选择初始化overlap场景id=3532
2025-07-28 20:31:36,463 - INFO - 方案 #1: 初始选择后，当前总时长=2.68秒
2025-07-28 20:31:36,463 - INFO - 方案 #1: 额外between选择后，当前总时长=2.68秒
2025-07-28 20:31:36,463 - INFO - 方案 #1: 场景总时长(2.68秒)小于音频时长(2.94秒)，需要延伸填充
2025-07-28 20:31:36,463 - INFO - 方案 #1: 最后一个场景ID: 3532
2025-07-28 20:31:36,463 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 3531
2025-07-28 20:31:36,463 - INFO - 方案 #1: 需要填充时长: 0.26秒
2025-07-28 20:31:36,463 - INFO - 方案 #1: 追加场景 scene_id=3533 (裁剪至 0.26秒)
2025-07-28 20:31:36,463 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 20:31:36,463 - INFO - 方案 #1 调整/填充后最终总时长: 2.94秒
2025-07-28 20:31:36,463 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:36,463 - INFO - 开始生成方案 #2
2025-07-28 20:31:36,463 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,463 - INFO - 开始生成方案 #3
2025-07-28 20:31:36,463 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,463 - INFO - 开始生成方案 #4
2025-07-28 20:31:36,463 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,464 - INFO - 开始生成方案 #5
2025-07-28 20:31:36,464 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,464 - INFO - 开始生成方案 #6
2025-07-28 20:31:36,464 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:36,464 - INFO - ========== 字幕 #47 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:36,464 - INFO - 
----- 处理字幕 #47 的方案 #1 -----
2025-07-28 20:31:36,464 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-28 20:31:36,464 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpozi2slhf
2025-07-28 20:31:36,464 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3530.mp4 (确认存在: True)
2025-07-28 20:31:36,464 - INFO - 添加场景ID=3530，时长=1.12秒，累计时长=1.12秒
2025-07-28 20:31:36,464 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3532.mp4 (确认存在: True)
2025-07-28 20:31:36,464 - INFO - 添加场景ID=3532，时长=1.56秒，累计时长=2.68秒
2025-07-28 20:31:36,464 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3533.mp4 (确认存在: True)
2025-07-28 20:31:36,464 - INFO - 添加场景ID=3533，时长=1.64秒，累计时长=4.32秒
2025-07-28 20:31:36,465 - INFO - 准备合并 3 个场景文件，总时长约 4.32秒
2025-07-28 20:31:36,465 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3530.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3532.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3533.mp4'

2025-07-28 20:31:36,465 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpozi2slhf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpozi2slhf\temp_combined.mp4
2025-07-28 20:31:36,618 - INFO - 合并后的视频时长: 4.39秒，目标音频时长: 2.94秒
2025-07-28 20:31:36,618 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpozi2slhf\temp_combined.mp4 -ss 0 -to 2.939 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-28 20:31:36,879 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:36,879 - INFO - 目标音频时长: 2.94秒
2025-07-28 20:31:36,879 - INFO - 实际视频时长: 2.98秒
2025-07-28 20:31:36,879 - INFO - 时长差异: 0.04秒 (1.50%)
2025-07-28 20:31:36,879 - INFO - ==========================================
2025-07-28 20:31:36,879 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:36,879 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-28 20:31:36,880 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpozi2slhf
2025-07-28 20:31:36,934 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:36,934 - INFO -   - 音频时长: 2.94秒
2025-07-28 20:31:36,934 - INFO -   - 视频时长: 2.98秒
2025-07-28 20:31:36,934 - INFO -   - 时长差异: 0.04秒 (1.50%)
2025-07-28 20:31:36,934 - INFO - 
字幕 #47 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:36,934 - INFO - 生成的视频文件:
2025-07-28 20:31:36,934 - INFO -   1. F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-07-28 20:31:36,934 - INFO - ========== 字幕 #47 处理结束 ==========

