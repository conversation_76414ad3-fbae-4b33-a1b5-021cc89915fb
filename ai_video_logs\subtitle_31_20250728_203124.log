2025-07-28 20:31:24,973 - INFO - ========== 字幕 #31 处理开始 ==========
2025-07-28 20:31:24,973 - INFO - 字幕内容: 在众人不可思议的注视下，女孩缓缓发力，竟真的将巨石推开。
2025-07-28 20:31:24,973 - INFO - 字幕序号: [2248, 2250]
2025-07-28 20:31:24,974 - INFO - 音频文件详情:
2025-07-28 20:31:24,974 - INFO -   - 路径: output\31.wav
2025-07-28 20:31:24,974 - INFO -   - 时长: 2.92秒
2025-07-28 20:31:24,974 - INFO -   - 验证音频时长: 2.92秒
2025-07-28 20:31:24,974 - INFO - 字幕时间戳信息:
2025-07-28 20:31:24,974 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:24,974 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:24,974 - INFO -   - 根据生成的音频时长(2.92秒)已调整字幕时间戳
2025-07-28 20:31:24,974 - INFO - ========== 开始为字幕 #31 生成 6 套场景方案 ==========
2025-07-28 20:31:24,974 - INFO - 开始查找字幕序号 [2248, 2250] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:24,974 - INFO - 找到related_overlap场景: scene_id=2341, 字幕#2248
2025-07-28 20:31:24,974 - INFO - 找到related_overlap场景: scene_id=2343, 字幕#2250
2025-07-28 20:31:24,975 - INFO - 找到related_between场景: scene_id=2333, 字幕#2248
2025-07-28 20:31:24,975 - INFO - 找到related_between场景: scene_id=2334, 字幕#2248
2025-07-28 20:31:24,975 - INFO - 找到related_between场景: scene_id=2335, 字幕#2248
2025-07-28 20:31:24,975 - INFO - 找到related_between场景: scene_id=2336, 字幕#2248
2025-07-28 20:31:24,975 - INFO - 找到related_between场景: scene_id=2337, 字幕#2248
2025-07-28 20:31:24,975 - INFO - 找到related_between场景: scene_id=2338, 字幕#2248
2025-07-28 20:31:24,975 - INFO - 找到related_between场景: scene_id=2339, 字幕#2248
2025-07-28 20:31:24,975 - INFO - 找到related_between场景: scene_id=2340, 字幕#2248
2025-07-28 20:31:24,975 - INFO - 字幕 #2248 找到 1 个overlap场景, 8 个between场景
2025-07-28 20:31:24,975 - INFO - 字幕 #2250 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:24,975 - INFO - 共收集 2 个未使用的overlap场景和 8 个未使用的between场景
2025-07-28 20:31:24,975 - INFO - 开始生成方案 #1
2025-07-28 20:31:24,975 - INFO - 方案 #1: 为字幕#2248选择初始化overlap场景id=2341
2025-07-28 20:31:24,975 - INFO - 方案 #1: 为字幕#2250选择初始化overlap场景id=2343
2025-07-28 20:31:24,975 - INFO - 方案 #1: 初始选择后，当前总时长=2.16秒
2025-07-28 20:31:24,975 - INFO - 方案 #1: 额外between选择后，当前总时长=2.16秒
2025-07-28 20:31:24,975 - INFO - 方案 #1: 额外添加between场景id=2338, 当前总时长=3.04秒
2025-07-28 20:31:24,975 - INFO - 方案 #1: 场景总时长(3.04秒)大于音频时长(2.92秒)，需要裁剪
2025-07-28 20:31:24,975 - INFO - 调整前总时长: 3.04秒, 目标时长: 2.92秒
2025-07-28 20:31:24,975 - INFO - 需要裁剪 0.11秒
2025-07-28 20:31:24,975 - INFO - 裁剪最长场景ID=2343：从1.24秒裁剪至1.12秒
2025-07-28 20:31:24,975 - INFO - 调整后总时长: 2.92秒，与目标时长差异: 0.00秒
2025-07-28 20:31:24,975 - INFO - 方案 #1 调整/填充后最终总时长: 2.92秒
2025-07-28 20:31:24,975 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:24,976 - INFO - 开始生成方案 #2
2025-07-28 20:31:24,976 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:24,976 - INFO - 方案 #2: 为字幕#2248选择初始化between场景id=2337
2025-07-28 20:31:24,976 - INFO - 方案 #2: 额外between选择后，当前总时长=1.24秒
2025-07-28 20:31:24,976 - INFO - 方案 #2: 额外添加between场景id=2336, 当前总时长=2.84秒
2025-07-28 20:31:24,976 - INFO - 方案 #2: 额外添加between场景id=2334, 当前总时长=4.12秒
2025-07-28 20:31:24,976 - INFO - 方案 #2: 场景总时长(4.12秒)大于音频时长(2.92秒)，需要裁剪
2025-07-28 20:31:24,976 - INFO - 调整前总时长: 4.12秒, 目标时长: 2.92秒
2025-07-28 20:31:24,976 - INFO - 需要裁剪 1.19秒
2025-07-28 20:31:24,976 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:24,976 - INFO - 裁剪场景ID=2336：从1.60秒裁剪至1.00秒
2025-07-28 20:31:24,976 - INFO - 裁剪场景ID=2334：从1.28秒裁剪至1.00秒
2025-07-28 20:31:24,976 - INFO - 裁剪场景ID=2337：从1.24秒裁剪至1.00秒
2025-07-28 20:31:24,976 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.08秒
2025-07-28 20:31:24,976 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.08秒
2025-07-28 20:31:24,976 - INFO - 方案 #2 调整/填充后最终总时长: 3.00秒
2025-07-28 20:31:24,976 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:24,976 - INFO - 开始生成方案 #3
2025-07-28 20:31:24,976 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:24,976 - INFO - 方案 #3: 为字幕#2248选择初始化between场景id=2339
2025-07-28 20:31:24,976 - INFO - 方案 #3: 额外between选择后，当前总时长=0.56秒
2025-07-28 20:31:24,976 - INFO - 方案 #3: 额外添加between场景id=2340, 当前总时长=1.64秒
2025-07-28 20:31:24,976 - INFO - 方案 #3: 额外添加between场景id=2335, 当前总时长=2.08秒
2025-07-28 20:31:24,976 - INFO - 方案 #3: 额外添加between场景id=2333, 当前总时长=3.60秒
2025-07-28 20:31:24,976 - INFO - 方案 #3: 场景总时长(3.60秒)大于音频时长(2.92秒)，需要裁剪
2025-07-28 20:31:24,976 - INFO - 调整前总时长: 3.60秒, 目标时长: 2.92秒
2025-07-28 20:31:24,976 - INFO - 需要裁剪 0.67秒
2025-07-28 20:31:24,976 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:24,976 - INFO - 裁剪场景ID=2333：从1.52秒裁剪至1.00秒
2025-07-28 20:31:24,976 - INFO - 裁剪场景ID=2340：从1.08秒裁剪至1.00秒
2025-07-28 20:31:24,976 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.07秒
2025-07-28 20:31:24,976 - INFO - 移除场景ID=2335，时长=0.44秒
2025-07-28 20:31:24,976 - INFO - 调整后总时长: 2.56秒，与目标时长差异: 0.36秒
2025-07-28 20:31:24,976 - INFO - 方案 #3 调整/填充后最终总时长: 2.56秒
2025-07-28 20:31:24,976 - INFO - 方案 #3 添加到方案列表
2025-07-28 20:31:24,976 - INFO - 开始生成方案 #4
2025-07-28 20:31:24,976 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:24,976 - INFO - 开始生成方案 #5
2025-07-28 20:31:24,976 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:24,976 - INFO - 开始生成方案 #6
2025-07-28 20:31:24,976 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:24,976 - INFO - ========== 字幕 #31 的 3 套有效场景方案生成完成 ==========
2025-07-28 20:31:24,976 - INFO - 
----- 处理字幕 #31 的方案 #1 -----
2025-07-28 20:31:24,976 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-28 20:31:24,977 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmsavzkm1
2025-07-28 20:31:24,977 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2341.mp4 (确认存在: True)
2025-07-28 20:31:24,977 - INFO - 添加场景ID=2341，时长=0.92秒，累计时长=0.92秒
2025-07-28 20:31:24,977 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2343.mp4 (确认存在: True)
2025-07-28 20:31:24,977 - INFO - 添加场景ID=2343，时长=1.24秒，累计时长=2.16秒
2025-07-28 20:31:24,977 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2338.mp4 (确认存在: True)
2025-07-28 20:31:24,977 - INFO - 添加场景ID=2338，时长=0.88秒，累计时长=3.04秒
2025-07-28 20:31:24,977 - INFO - 准备合并 3 个场景文件，总时长约 3.04秒
2025-07-28 20:31:24,977 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2341.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2343.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2338.mp4'

2025-07-28 20:31:24,977 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmsavzkm1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmsavzkm1\temp_combined.mp4
2025-07-28 20:31:25,141 - INFO - 合并后的视频时长: 3.11秒，目标音频时长: 2.92秒
2025-07-28 20:31:25,141 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmsavzkm1\temp_combined.mp4 -ss 0 -to 2.925 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-28 20:31:25,398 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:25,399 - INFO - 目标音频时长: 2.92秒
2025-07-28 20:31:25,399 - INFO - 实际视频时长: 2.98秒
2025-07-28 20:31:25,399 - INFO - 时长差异: 0.06秒 (1.98%)
2025-07-28 20:31:25,399 - INFO - ==========================================
2025-07-28 20:31:25,399 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:25,399 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-28 20:31:25,399 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmsavzkm1
2025-07-28 20:31:25,441 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:25,441 - INFO -   - 音频时长: 2.92秒
2025-07-28 20:31:25,441 - INFO -   - 视频时长: 2.98秒
2025-07-28 20:31:25,441 - INFO -   - 时长差异: 0.06秒 (1.98%)
2025-07-28 20:31:25,441 - INFO - 
----- 处理字幕 #31 的方案 #2 -----
2025-07-28 20:31:25,441 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-28 20:31:25,441 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpa5l5m9gt
2025-07-28 20:31:25,442 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2337.mp4 (确认存在: True)
2025-07-28 20:31:25,442 - INFO - 添加场景ID=2337，时长=1.24秒，累计时长=1.24秒
2025-07-28 20:31:25,442 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2336.mp4 (确认存在: True)
2025-07-28 20:31:25,442 - INFO - 添加场景ID=2336，时长=1.60秒，累计时长=2.84秒
2025-07-28 20:31:25,442 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2334.mp4 (确认存在: True)
2025-07-28 20:31:25,442 - INFO - 添加场景ID=2334，时长=1.28秒，累计时长=4.12秒
2025-07-28 20:31:25,442 - INFO - 准备合并 3 个场景文件，总时长约 4.12秒
2025-07-28 20:31:25,442 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2337.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2336.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2334.mp4'

2025-07-28 20:31:25,442 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpa5l5m9gt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpa5l5m9gt\temp_combined.mp4
2025-07-28 20:31:25,611 - INFO - 合并后的视频时长: 4.19秒，目标音频时长: 2.92秒
2025-07-28 20:31:25,611 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpa5l5m9gt\temp_combined.mp4 -ss 0 -to 2.925 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-28 20:31:25,896 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:25,896 - INFO - 目标音频时长: 2.92秒
2025-07-28 20:31:25,896 - INFO - 实际视频时长: 2.98秒
2025-07-28 20:31:25,896 - INFO - 时长差异: 0.06秒 (1.98%)
2025-07-28 20:31:25,896 - INFO - ==========================================
2025-07-28 20:31:25,896 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:25,896 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-28 20:31:25,897 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpa5l5m9gt
2025-07-28 20:31:25,947 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:25,947 - INFO -   - 音频时长: 2.92秒
2025-07-28 20:31:25,948 - INFO -   - 视频时长: 2.98秒
2025-07-28 20:31:25,948 - INFO -   - 时长差异: 0.06秒 (1.98%)
2025-07-28 20:31:25,948 - INFO - 
----- 处理字幕 #31 的方案 #3 -----
2025-07-28 20:31:25,948 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-28 20:31:25,948 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw6psls12
2025-07-28 20:31:25,948 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2339.mp4 (确认存在: True)
2025-07-28 20:31:25,949 - INFO - 添加场景ID=2339，时长=0.56秒，累计时长=0.56秒
2025-07-28 20:31:25,949 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2340.mp4 (确认存在: True)
2025-07-28 20:31:25,949 - INFO - 添加场景ID=2340，时长=1.08秒，累计时长=1.64秒
2025-07-28 20:31:25,949 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2333.mp4 (确认存在: True)
2025-07-28 20:31:25,949 - INFO - 添加场景ID=2333，时长=1.52秒，累计时长=3.16秒
2025-07-28 20:31:25,949 - INFO - 准备合并 3 个场景文件，总时长约 3.16秒
2025-07-28 20:31:25,949 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2339.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2340.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2333.mp4'

2025-07-28 20:31:25,949 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpw6psls12\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpw6psls12\temp_combined.mp4
2025-07-28 20:31:26,102 - INFO - 合并后的视频时长: 3.23秒，目标音频时长: 2.92秒
2025-07-28 20:31:26,102 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpw6psls12\temp_combined.mp4 -ss 0 -to 2.925 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-28 20:31:26,359 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:26,359 - INFO - 目标音频时长: 2.92秒
2025-07-28 20:31:26,359 - INFO - 实际视频时长: 2.98秒
2025-07-28 20:31:26,359 - INFO - 时长差异: 0.06秒 (1.98%)
2025-07-28 20:31:26,359 - INFO - ==========================================
2025-07-28 20:31:26,359 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:26,359 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-28 20:31:26,359 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw6psls12
2025-07-28 20:31:26,402 - INFO - 方案 #3 处理完成:
2025-07-28 20:31:26,402 - INFO -   - 音频时长: 2.92秒
2025-07-28 20:31:26,402 - INFO -   - 视频时长: 2.98秒
2025-07-28 20:31:26,402 - INFO -   - 时长差异: 0.06秒 (1.98%)
2025-07-28 20:31:26,402 - INFO - 
字幕 #31 处理完成，成功生成 3/3 套方案
2025-07-28 20:31:26,402 - INFO - 生成的视频文件:
2025-07-28 20:31:26,402 - INFO -   1. F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-07-28 20:31:26,402 - INFO -   2. F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-07-28 20:31:26,402 - INFO -   3. F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-07-28 20:31:26,402 - INFO - ========== 字幕 #31 处理结束 ==========

