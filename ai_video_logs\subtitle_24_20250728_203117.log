2025-07-28 20:31:17,805 - INFO - ========== 字幕 #24 处理开始 ==========
2025-07-28 20:31:17,805 - INFO - 字幕内容: 危急关头，女孩手持神弓，一箭射出，竟将凶猛的熊瞎子当场射杀。
2025-07-28 20:31:17,805 - INFO - 字幕序号: [1931, 1949]
2025-07-28 20:31:17,805 - INFO - 音频文件详情:
2025-07-28 20:31:17,805 - INFO -   - 路径: output\24.wav
2025-07-28 20:31:17,805 - INFO -   - 时长: 3.71秒
2025-07-28 20:31:17,806 - INFO -   - 验证音频时长: 3.71秒
2025-07-28 20:31:17,806 - INFO - 字幕时间戳信息:
2025-07-28 20:31:17,806 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:17,806 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:17,806 - INFO -   - 根据生成的音频时长(3.71秒)已调整字幕时间戳
2025-07-28 20:31:17,806 - INFO - ========== 开始为字幕 #24 生成 6 套场景方案 ==========
2025-07-28 20:31:17,806 - INFO - 开始查找字幕序号 [1931, 1949] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:17,806 - INFO - 找到related_overlap场景: scene_id=1996, 字幕#1931
2025-07-28 20:31:17,806 - INFO - 找到related_overlap场景: scene_id=2036, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=1997, 字幕#1931
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=1998, 字幕#1931
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=1999, 字幕#1931
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2000, 字幕#1931
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2001, 字幕#1931
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2002, 字幕#1931
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2024, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2025, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2026, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2027, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2028, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2029, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2030, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2031, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2032, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2033, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2034, 字幕#1949
2025-07-28 20:31:17,807 - INFO - 找到related_between场景: scene_id=2035, 字幕#1949
2025-07-28 20:31:17,808 - INFO - 字幕 #1931 找到 1 个overlap场景, 6 个between场景
2025-07-28 20:31:17,808 - INFO - 字幕 #1949 找到 1 个overlap场景, 12 个between场景
2025-07-28 20:31:17,808 - INFO - 共收集 2 个未使用的overlap场景和 18 个未使用的between场景
2025-07-28 20:31:17,808 - INFO - 开始生成方案 #1
2025-07-28 20:31:17,808 - INFO - 方案 #1: 为字幕#1931选择初始化overlap场景id=1996
2025-07-28 20:31:17,808 - INFO - 方案 #1: 为字幕#1949选择初始化overlap场景id=2036
2025-07-28 20:31:17,808 - INFO - 方案 #1: 初始选择后，当前总时长=1.52秒
2025-07-28 20:31:17,808 - INFO - 方案 #1: 额外between选择后，当前总时长=1.52秒
2025-07-28 20:31:17,808 - INFO - 方案 #1: 额外添加between场景id=1999, 当前总时长=2.28秒
2025-07-28 20:31:17,808 - INFO - 方案 #1: 额外添加between场景id=2024, 当前总时长=4.52秒
2025-07-28 20:31:17,808 - INFO - 方案 #1: 场景总时长(4.52秒)大于音频时长(3.71秒)，需要裁剪
2025-07-28 20:31:17,808 - INFO - 调整前总时长: 4.52秒, 目标时长: 3.71秒
2025-07-28 20:31:17,808 - INFO - 需要裁剪 0.80秒
2025-07-28 20:31:17,808 - INFO - 裁剪最长场景ID=2024：从2.24秒裁剪至1.44秒
2025-07-28 20:31:17,808 - INFO - 调整后总时长: 3.71秒，与目标时长差异: 0.00秒
2025-07-28 20:31:17,808 - INFO - 方案 #1 调整/填充后最终总时长: 3.71秒
2025-07-28 20:31:17,808 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:17,808 - INFO - 开始生成方案 #2
2025-07-28 20:31:17,808 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:17,808 - INFO - 方案 #2: 为字幕#1931选择初始化between场景id=2002
2025-07-28 20:31:17,808 - INFO - 方案 #2: 为字幕#1949选择初始化between场景id=2027
2025-07-28 20:31:17,808 - INFO - 方案 #2: 额外between选择后，当前总时长=1.60秒
2025-07-28 20:31:17,808 - INFO - 方案 #2: 额外添加between场景id=2028, 当前总时长=2.00秒
2025-07-28 20:31:17,808 - INFO - 方案 #2: 额外添加between场景id=1998, 当前总时长=2.76秒
2025-07-28 20:31:17,808 - INFO - 方案 #2: 额外添加between场景id=2035, 当前总时长=3.52秒
2025-07-28 20:31:17,808 - INFO - 方案 #2: 额外添加between场景id=1997, 当前总时长=5.08秒
2025-07-28 20:31:17,808 - INFO - 方案 #2: 场景总时长(5.08秒)大于音频时长(3.71秒)，需要裁剪
2025-07-28 20:31:17,808 - INFO - 调整前总时长: 5.08秒, 目标时长: 3.71秒
2025-07-28 20:31:17,808 - INFO - 需要裁剪 1.36秒
2025-07-28 20:31:17,808 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:17,808 - INFO - 裁剪场景ID=1997：从1.56秒裁剪至1.00秒
2025-07-28 20:31:17,808 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.80秒
2025-07-28 20:31:17,808 - INFO - 移除场景ID=2028，时长=0.40秒
2025-07-28 20:31:17,808 - INFO - 移除场景ID=2002，时长=0.64秒
2025-07-28 20:31:17,808 - INFO - 调整后总时长: 3.48秒，与目标时长差异: 0.24秒
2025-07-28 20:31:17,808 - INFO - 方案 #2 调整/填充后最终总时长: 3.48秒
2025-07-28 20:31:17,808 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:17,808 - INFO - 开始生成方案 #3
2025-07-28 20:31:17,808 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:17,808 - INFO - 方案 #3: 为字幕#1931选择初始化between场景id=2000
2025-07-28 20:31:17,808 - INFO - 方案 #3: 为字幕#1949选择初始化between场景id=2030
2025-07-28 20:31:17,808 - INFO - 方案 #3: 额外between选择后，当前总时长=1.44秒
2025-07-28 20:31:17,808 - INFO - 方案 #3: 额外添加between场景id=2025, 当前总时长=3.12秒
2025-07-28 20:31:17,808 - INFO - 方案 #3: 额外添加between场景id=2026, 当前总时长=4.36秒
2025-07-28 20:31:17,808 - INFO - 方案 #3: 场景总时长(4.36秒)大于音频时长(3.71秒)，需要裁剪
2025-07-28 20:31:17,808 - INFO - 调整前总时长: 4.36秒, 目标时长: 3.71秒
2025-07-28 20:31:17,808 - INFO - 需要裁剪 0.64秒
2025-07-28 20:31:17,808 - INFO - 裁剪最长场景ID=2025：从1.68秒裁剪至1.04秒
2025-07-28 20:31:17,808 - INFO - 调整后总时长: 3.71秒，与目标时长差异: 0.00秒
2025-07-28 20:31:17,808 - INFO - 方案 #3 调整/填充后最终总时长: 3.71秒
2025-07-28 20:31:17,809 - INFO - 方案 #3 添加到方案列表
2025-07-28 20:31:17,809 - INFO - 开始生成方案 #4
2025-07-28 20:31:17,809 - INFO - 方案 #4: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:17,809 - INFO - 方案 #4: 为字幕#1931选择初始化between场景id=2001
2025-07-28 20:31:17,809 - INFO - 方案 #4: 为字幕#1949选择初始化between场景id=2034
2025-07-28 20:31:17,809 - INFO - 方案 #4: 额外between选择后，当前总时长=1.72秒
2025-07-28 20:31:17,809 - INFO - 方案 #4: 额外添加between场景id=2032, 当前总时长=2.56秒
2025-07-28 20:31:17,809 - INFO - 方案 #4: 额外添加between场景id=2029, 当前总时长=3.28秒
2025-07-28 20:31:17,809 - INFO - 方案 #4: 额外添加between场景id=2031, 当前总时长=4.24秒
2025-07-28 20:31:17,809 - INFO - 方案 #4: 场景总时长(4.24秒)大于音频时长(3.71秒)，需要裁剪
2025-07-28 20:31:17,809 - INFO - 调整前总时长: 4.24秒, 目标时长: 3.71秒
2025-07-28 20:31:17,809 - INFO - 需要裁剪 0.53秒
2025-07-28 20:31:17,809 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:17,809 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.53秒
2025-07-28 20:31:17,809 - INFO - 移除场景ID=2029，时长=0.72秒
2025-07-28 20:31:17,809 - INFO - 调整后总时长: 3.52秒，与目标时长差异: 0.19秒
2025-07-28 20:31:17,809 - INFO - 方案 #4 调整/填充后最终总时长: 3.52秒
2025-07-28 20:31:17,809 - INFO - 方案 #4 添加到方案列表
2025-07-28 20:31:17,809 - INFO - 开始生成方案 #5
2025-07-28 20:31:17,809 - INFO - 方案 #5: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:17,809 - INFO - 方案 #5: 为字幕#1949选择初始化between场景id=2033
2025-07-28 20:31:17,809 - INFO - 方案 #5: 额外between选择后，当前总时长=0.44秒
2025-07-28 20:31:17,809 - INFO - 方案 #5: 场景总时长(0.44秒)小于音频时长(3.71秒)，需要延伸填充
2025-07-28 20:31:17,809 - INFO - 方案 #5: 最后一个场景ID: 2033
2025-07-28 20:31:17,809 - INFO - 方案 #5: 找到最后一个场景在原始列表中的索引: 2032
2025-07-28 20:31:17,809 - INFO - 方案 #5: 需要填充时长: 3.27秒
2025-07-28 20:31:17,809 - INFO - 方案 #5: 跳过已使用的场景: scene_id=2034
2025-07-28 20:31:17,809 - INFO - 方案 #5: 跳过已使用的场景: scene_id=2035
2025-07-28 20:31:17,809 - INFO - 方案 #5: 跳过已使用的场景: scene_id=2036
2025-07-28 20:31:17,809 - INFO - 方案 #5: 追加场景 scene_id=2037 (完整时长 0.76秒)
2025-07-28 20:31:17,809 - INFO - 方案 #5: 追加场景 scene_id=2038 (完整时长 1.52秒)
2025-07-28 20:31:17,809 - INFO - 方案 #5: 追加场景 scene_id=2039 (裁剪至 1.00秒)
2025-07-28 20:31:17,809 - INFO - 方案 #5: 成功填充至目标时长
2025-07-28 20:31:17,809 - INFO - 方案 #5 调整/填充后最终总时长: 3.71秒
2025-07-28 20:31:17,809 - INFO - 方案 #5 添加到方案列表
2025-07-28 20:31:17,809 - INFO - 开始生成方案 #6
2025-07-28 20:31:17,809 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:17,809 - INFO - ========== 字幕 #24 的 5 套有效场景方案生成完成 ==========
2025-07-28 20:31:17,810 - INFO - 
----- 处理字幕 #24 的方案 #1 -----
2025-07-28 20:31:17,810 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-28 20:31:17,810 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgptkllc6
2025-07-28 20:31:17,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1996.mp4 (确认存在: True)
2025-07-28 20:31:17,811 - INFO - 添加场景ID=1996，时长=0.48秒，累计时长=0.48秒
2025-07-28 20:31:17,811 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2036.mp4 (确认存在: True)
2025-07-28 20:31:17,811 - INFO - 添加场景ID=2036，时长=1.04秒，累计时长=1.52秒
2025-07-28 20:31:17,811 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1999.mp4 (确认存在: True)
2025-07-28 20:31:17,811 - INFO - 添加场景ID=1999，时长=0.76秒，累计时长=2.28秒
2025-07-28 20:31:17,811 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2024.mp4 (确认存在: True)
2025-07-28 20:31:17,811 - INFO - 添加场景ID=2024，时长=2.24秒，累计时长=4.52秒
2025-07-28 20:31:17,811 - INFO - 准备合并 4 个场景文件，总时长约 4.52秒
2025-07-28 20:31:17,811 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1996.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2036.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1999.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2024.mp4'

2025-07-28 20:31:17,811 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgptkllc6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgptkllc6\temp_combined.mp4
2025-07-28 20:31:17,952 - INFO - 合并后的视频时长: 4.61秒，目标音频时长: 3.71秒
2025-07-28 20:31:17,952 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpgptkllc6\temp_combined.mp4 -ss 0 -to 3.715 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-28 20:31:18,246 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:18,246 - INFO - 目标音频时长: 3.71秒
2025-07-28 20:31:18,246 - INFO - 实际视频时长: 3.74秒
2025-07-28 20:31:18,246 - INFO - 时长差异: 0.03秒 (0.75%)
2025-07-28 20:31:18,246 - INFO - ==========================================
2025-07-28 20:31:18,246 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:18,246 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-28 20:31:18,247 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgptkllc6
2025-07-28 20:31:18,288 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:18,289 - INFO -   - 音频时长: 3.71秒
2025-07-28 20:31:18,289 - INFO -   - 视频时长: 3.74秒
2025-07-28 20:31:18,289 - INFO -   - 时长差异: 0.03秒 (0.75%)
2025-07-28 20:31:18,289 - INFO - 
----- 处理字幕 #24 的方案 #2 -----
2025-07-28 20:31:18,289 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-28 20:31:18,289 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi97_caeg
2025-07-28 20:31:18,290 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2027.mp4 (确认存在: True)
2025-07-28 20:31:18,290 - INFO - 添加场景ID=2027，时长=0.96秒，累计时长=0.96秒
2025-07-28 20:31:18,290 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1998.mp4 (确认存在: True)
2025-07-28 20:31:18,290 - INFO - 添加场景ID=1998，时长=0.76秒，累计时长=1.72秒
2025-07-28 20:31:18,290 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2035.mp4 (确认存在: True)
2025-07-28 20:31:18,290 - INFO - 添加场景ID=2035，时长=0.76秒，累计时长=2.48秒
2025-07-28 20:31:18,290 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1997.mp4 (确认存在: True)
2025-07-28 20:31:18,290 - INFO - 添加场景ID=1997，时长=1.56秒，累计时长=4.04秒
2025-07-28 20:31:18,290 - INFO - 准备合并 4 个场景文件，总时长约 4.04秒
2025-07-28 20:31:18,290 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2027.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1998.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2035.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1997.mp4'

2025-07-28 20:31:18,290 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpi97_caeg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpi97_caeg\temp_combined.mp4
2025-07-28 20:31:18,452 - INFO - 合并后的视频时长: 4.13秒，目标音频时长: 3.71秒
2025-07-28 20:31:18,452 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpi97_caeg\temp_combined.mp4 -ss 0 -to 3.715 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-28 20:31:18,739 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:18,739 - INFO - 目标音频时长: 3.71秒
2025-07-28 20:31:18,739 - INFO - 实际视频时长: 3.74秒
2025-07-28 20:31:18,739 - INFO - 时长差异: 0.03秒 (0.75%)
2025-07-28 20:31:18,739 - INFO - ==========================================
2025-07-28 20:31:18,739 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:18,739 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-28 20:31:18,740 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi97_caeg
2025-07-28 20:31:18,785 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:18,785 - INFO -   - 音频时长: 3.71秒
2025-07-28 20:31:18,785 - INFO -   - 视频时长: 3.74秒
2025-07-28 20:31:18,786 - INFO -   - 时长差异: 0.03秒 (0.75%)
2025-07-28 20:31:18,786 - INFO - 
----- 处理字幕 #24 的方案 #3 -----
2025-07-28 20:31:18,786 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-28 20:31:18,786 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp14_7ds6g
2025-07-28 20:31:18,786 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2000.mp4 (确认存在: True)
2025-07-28 20:31:18,786 - INFO - 添加场景ID=2000，时长=0.84秒，累计时长=0.84秒
2025-07-28 20:31:18,787 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2030.mp4 (确认存在: True)
2025-07-28 20:31:18,787 - INFO - 添加场景ID=2030，时长=0.60秒，累计时长=1.44秒
2025-07-28 20:31:18,787 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2025.mp4 (确认存在: True)
2025-07-28 20:31:18,787 - INFO - 添加场景ID=2025，时长=1.68秒，累计时长=3.12秒
2025-07-28 20:31:18,787 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2026.mp4 (确认存在: True)
2025-07-28 20:31:18,787 - INFO - 添加场景ID=2026，时长=1.24秒，累计时长=4.36秒
2025-07-28 20:31:18,787 - INFO - 准备合并 4 个场景文件，总时长约 4.36秒
2025-07-28 20:31:18,787 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2000.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2030.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2025.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2026.mp4'

2025-07-28 20:31:18,787 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp14_7ds6g\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp14_7ds6g\temp_combined.mp4
2025-07-28 20:31:18,935 - INFO - 合并后的视频时长: 4.45秒，目标音频时长: 3.71秒
2025-07-28 20:31:18,935 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp14_7ds6g\temp_combined.mp4 -ss 0 -to 3.715 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-28 20:31:19,206 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:19,206 - INFO - 目标音频时长: 3.71秒
2025-07-28 20:31:19,206 - INFO - 实际视频时长: 3.74秒
2025-07-28 20:31:19,206 - INFO - 时长差异: 0.03秒 (0.75%)
2025-07-28 20:31:19,206 - INFO - ==========================================
2025-07-28 20:31:19,206 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:19,206 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-28 20:31:19,207 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp14_7ds6g
2025-07-28 20:31:19,247 - INFO - 方案 #3 处理完成:
2025-07-28 20:31:19,247 - INFO -   - 音频时长: 3.71秒
2025-07-28 20:31:19,247 - INFO -   - 视频时长: 3.74秒
2025-07-28 20:31:19,247 - INFO -   - 时长差异: 0.03秒 (0.75%)
2025-07-28 20:31:19,247 - INFO - 
----- 处理字幕 #24 的方案 #4 -----
2025-07-28 20:31:19,247 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_4.mp4
2025-07-28 20:31:19,248 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgtplkfrn
2025-07-28 20:31:19,248 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2001.mp4 (确认存在: True)
2025-07-28 20:31:19,248 - INFO - 添加场景ID=2001，时长=0.88秒，累计时长=0.88秒
2025-07-28 20:31:19,248 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2034.mp4 (确认存在: True)
2025-07-28 20:31:19,248 - INFO - 添加场景ID=2034，时长=0.84秒，累计时长=1.72秒
2025-07-28 20:31:19,248 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2032.mp4 (确认存在: True)
2025-07-28 20:31:19,248 - INFO - 添加场景ID=2032，时长=0.84秒，累计时长=2.56秒
2025-07-28 20:31:19,248 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2031.mp4 (确认存在: True)
2025-07-28 20:31:19,248 - INFO - 添加场景ID=2031，时长=0.96秒，累计时长=3.52秒
2025-07-28 20:31:19,249 - INFO - 准备合并 4 个场景文件，总时长约 3.52秒
2025-07-28 20:31:19,249 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2001.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2034.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2032.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2031.mp4'

2025-07-28 20:31:19,249 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpgtplkfrn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpgtplkfrn\temp_combined.mp4
2025-07-28 20:31:19,409 - INFO - 合并后的视频时长: 3.61秒，目标音频时长: 3.71秒
2025-07-28 20:31:19,409 - ERROR - 合并后的视频时长(3.61秒)小于音频时长(3.71秒)，无法进行精确裁剪
2025-07-28 20:31:19,409 - WARNING - 将直接使用合并后的视频，可能导致音视频不同步
2025-07-28 20:31:19,477 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:19,477 - INFO - 目标音频时长: 3.71秒
2025-07-28 20:31:19,477 - INFO - 实际视频时长: 3.61秒
2025-07-28 20:31:19,477 - INFO - 时长差异: 0.10秒 (2.75%)
2025-07-28 20:31:19,477 - INFO - ==========================================
2025-07-28 20:31:19,477 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:19,477 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_4.mp4
2025-07-28 20:31:19,478 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpgtplkfrn
2025-07-28 20:31:19,544 - INFO - 方案 #4 处理完成:
2025-07-28 20:31:19,544 - INFO -   - 音频时长: 3.71秒
2025-07-28 20:31:19,545 - INFO -   - 视频时长: 3.61秒
2025-07-28 20:31:19,545 - INFO -   - 时长差异: 0.10秒 (2.75%)
2025-07-28 20:31:19,545 - INFO - 
----- 处理字幕 #24 的方案 #5 -----
2025-07-28 20:31:19,545 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_5.mp4
2025-07-28 20:31:19,545 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbb8akl2f
2025-07-28 20:31:19,545 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2033.mp4 (确认存在: True)
2025-07-28 20:31:19,545 - INFO - 添加场景ID=2033，时长=0.44秒，累计时长=0.44秒
2025-07-28 20:31:19,545 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2037.mp4 (确认存在: True)
2025-07-28 20:31:19,545 - INFO - 添加场景ID=2037，时长=0.76秒，累计时长=1.20秒
2025-07-28 20:31:19,546 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2038.mp4 (确认存在: True)
2025-07-28 20:31:19,546 - INFO - 添加场景ID=2038，时长=1.52秒，累计时长=2.72秒
2025-07-28 20:31:19,546 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2039.mp4 (确认存在: True)
2025-07-28 20:31:19,546 - INFO - 添加场景ID=2039，时长=1.20秒，累计时长=3.92秒
2025-07-28 20:31:19,546 - INFO - 准备合并 4 个场景文件，总时长约 3.92秒
2025-07-28 20:31:19,546 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2033.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2037.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2038.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2039.mp4'

2025-07-28 20:31:19,546 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbb8akl2f\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbb8akl2f\temp_combined.mp4
2025-07-28 20:31:19,686 - INFO - 合并后的视频时长: 4.01秒，目标音频时长: 3.71秒
2025-07-28 20:31:19,686 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbb8akl2f\temp_combined.mp4 -ss 0 -to 3.715 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_5.mp4
2025-07-28 20:31:19,977 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:19,978 - INFO - 目标音频时长: 3.71秒
2025-07-28 20:31:19,978 - INFO - 实际视频时长: 3.74秒
2025-07-28 20:31:19,978 - INFO - 时长差异: 0.03秒 (0.75%)
2025-07-28 20:31:19,978 - INFO - ==========================================
2025-07-28 20:31:19,978 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:19,978 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_5.mp4
2025-07-28 20:31:19,978 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbb8akl2f
2025-07-28 20:31:20,018 - INFO - 方案 #5 处理完成:
2025-07-28 20:31:20,018 - INFO -   - 音频时长: 3.71秒
2025-07-28 20:31:20,018 - INFO -   - 视频时长: 3.74秒
2025-07-28 20:31:20,018 - INFO -   - 时长差异: 0.03秒 (0.75%)
2025-07-28 20:31:20,018 - INFO - 
字幕 #24 处理完成，成功生成 5/5 套方案
2025-07-28 20:31:20,018 - INFO - 生成的视频文件:
2025-07-28 20:31:20,018 - INFO -   1. F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-07-28 20:31:20,018 - INFO -   2. F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-07-28 20:31:20,018 - INFO -   3. F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-07-28 20:31:20,018 - INFO -   4. F:/github/aicut_auto/newcut_ai\24_4.mp4
2025-07-28 20:31:20,018 - INFO -   5. F:/github/aicut_auto/newcut_ai\24_5.mp4
2025-07-28 20:31:20,018 - INFO - ========== 字幕 #24 处理结束 ==========

