2025-07-28 20:31:24,121 - INFO - ========== 字幕 #30 处理开始 ==========
2025-07-28 20:31:24,121 - INFO - 字幕内容: 几个壮汉都搬不动的巨石，对女孩来说却轻而易举。
2025-07-28 20:31:24,121 - INFO - 字幕序号: [2205, 2218]
2025-07-28 20:31:24,121 - INFO - 音频文件详情:
2025-07-28 20:31:24,121 - INFO -   - 路径: output\30.wav
2025-07-28 20:31:24,121 - INFO -   - 时长: 2.50秒
2025-07-28 20:31:24,121 - INFO -   - 验证音频时长: 2.50秒
2025-07-28 20:31:24,121 - INFO - 字幕时间戳信息:
2025-07-28 20:31:24,122 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:24,122 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:24,122 - INFO -   - 根据生成的音频时长(2.50秒)已调整字幕时间戳
2025-07-28 20:31:24,122 - INFO - ========== 开始为字幕 #30 生成 6 套场景方案 ==========
2025-07-28 20:31:24,122 - INFO - 开始查找字幕序号 [2205, 2218] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:24,122 - INFO - 找到related_overlap场景: scene_id=2299, 字幕#2205
2025-07-28 20:31:24,122 - INFO - 找到related_overlap场景: scene_id=2300, 字幕#2205
2025-07-28 20:31:24,122 - INFO - 找到related_overlap场景: scene_id=2312, 字幕#2218
2025-07-28 20:31:24,122 - INFO - 找到related_overlap场景: scene_id=2313, 字幕#2218
2025-07-28 20:31:24,123 - INFO - 字幕 #2205 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:24,123 - INFO - 字幕 #2218 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:24,123 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:24,123 - INFO - 开始生成方案 #1
2025-07-28 20:31:24,123 - INFO - 方案 #1: 为字幕#2205选择初始化overlap场景id=2299
2025-07-28 20:31:24,123 - INFO - 方案 #1: 为字幕#2218选择初始化overlap场景id=2312
2025-07-28 20:31:24,123 - INFO - 方案 #1: 初始选择后，当前总时长=2.92秒
2025-07-28 20:31:24,123 - INFO - 方案 #1: 额外between选择后，当前总时长=2.92秒
2025-07-28 20:31:24,123 - INFO - 方案 #1: 场景总时长(2.92秒)大于音频时长(2.50秒)，需要裁剪
2025-07-28 20:31:24,123 - INFO - 调整前总时长: 2.92秒, 目标时长: 2.50秒
2025-07-28 20:31:24,123 - INFO - 需要裁剪 0.41秒
2025-07-28 20:31:24,123 - INFO - 裁剪最长场景ID=2312：从1.60秒裁剪至1.19秒
2025-07-28 20:31:24,123 - INFO - 调整后总时长: 2.50秒，与目标时长差异: 0.00秒
2025-07-28 20:31:24,123 - INFO - 方案 #1 调整/填充后最终总时长: 2.50秒
2025-07-28 20:31:24,123 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:24,123 - INFO - 开始生成方案 #2
2025-07-28 20:31:24,123 - INFO - 方案 #2: 为字幕#2205选择初始化overlap场景id=2300
2025-07-28 20:31:24,123 - INFO - 方案 #2: 为字幕#2218选择初始化overlap场景id=2313
2025-07-28 20:31:24,123 - INFO - 方案 #2: 初始选择后，当前总时长=3.96秒
2025-07-28 20:31:24,123 - INFO - 方案 #2: 额外between选择后，当前总时长=3.96秒
2025-07-28 20:31:24,123 - INFO - 方案 #2: 场景总时长(3.96秒)大于音频时长(2.50秒)，需要裁剪
2025-07-28 20:31:24,123 - INFO - 调整前总时长: 3.96秒, 目标时长: 2.50秒
2025-07-28 20:31:24,123 - INFO - 需要裁剪 1.45秒
2025-07-28 20:31:24,123 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:24,124 - INFO - 裁剪场景ID=2313：从2.32秒裁剪至1.00秒
2025-07-28 20:31:24,124 - INFO - 裁剪场景ID=2300：从1.64秒裁剪至1.50秒
2025-07-28 20:31:24,124 - INFO - 调整后总时长: 2.50秒，与目标时长差异: 0.00秒
2025-07-28 20:31:24,124 - INFO - 方案 #2 调整/填充后最终总时长: 2.50秒
2025-07-28 20:31:24,124 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:24,124 - INFO - 开始生成方案 #3
2025-07-28 20:31:24,124 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:24,124 - INFO - 开始生成方案 #4
2025-07-28 20:31:24,124 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:24,124 - INFO - 开始生成方案 #5
2025-07-28 20:31:24,124 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:24,124 - INFO - 开始生成方案 #6
2025-07-28 20:31:24,124 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:24,124 - INFO - ========== 字幕 #30 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:24,124 - INFO - 
----- 处理字幕 #30 的方案 #1 -----
2025-07-28 20:31:24,124 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-28 20:31:24,124 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqf4rlzu4
2025-07-28 20:31:24,124 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2299.mp4 (确认存在: True)
2025-07-28 20:31:24,124 - INFO - 添加场景ID=2299，时长=1.32秒，累计时长=1.32秒
2025-07-28 20:31:24,124 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2312.mp4 (确认存在: True)
2025-07-28 20:31:24,124 - INFO - 添加场景ID=2312，时长=1.60秒，累计时长=2.92秒
2025-07-28 20:31:24,124 - INFO - 准备合并 2 个场景文件，总时长约 2.92秒
2025-07-28 20:31:24,124 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2299.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2312.mp4'

2025-07-28 20:31:24,124 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqf4rlzu4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqf4rlzu4\temp_combined.mp4
2025-07-28 20:31:24,257 - INFO - 合并后的视频时长: 2.97秒，目标音频时长: 2.50秒
2025-07-28 20:31:24,257 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqf4rlzu4\temp_combined.mp4 -ss 0 -to 2.505 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-28 20:31:24,508 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:24,508 - INFO - 目标音频时长: 2.50秒
2025-07-28 20:31:24,508 - INFO - 实际视频时长: 2.54秒
2025-07-28 20:31:24,508 - INFO - 时长差异: 0.04秒 (1.52%)
2025-07-28 20:31:24,508 - INFO - ==========================================
2025-07-28 20:31:24,508 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:24,508 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-28 20:31:24,508 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqf4rlzu4
2025-07-28 20:31:24,552 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:24,552 - INFO -   - 音频时长: 2.50秒
2025-07-28 20:31:24,552 - INFO -   - 视频时长: 2.54秒
2025-07-28 20:31:24,552 - INFO -   - 时长差异: 0.04秒 (1.52%)
2025-07-28 20:31:24,552 - INFO - 
----- 处理字幕 #30 的方案 #2 -----
2025-07-28 20:31:24,552 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-28 20:31:24,552 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnreq3frl
2025-07-28 20:31:24,552 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2300.mp4 (确认存在: True)
2025-07-28 20:31:24,553 - INFO - 添加场景ID=2300，时长=1.64秒，累计时长=1.64秒
2025-07-28 20:31:24,553 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2313.mp4 (确认存在: True)
2025-07-28 20:31:24,553 - INFO - 添加场景ID=2313，时长=2.32秒，累计时长=3.96秒
2025-07-28 20:31:24,553 - INFO - 场景总时长(3.96秒)已达到音频时长(2.50秒)的1.5倍，停止添加场景
2025-07-28 20:31:24,553 - INFO - 准备合并 2 个场景文件，总时长约 3.96秒
2025-07-28 20:31:24,553 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2300.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2313.mp4'

2025-07-28 20:31:24,553 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnreq3frl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnreq3frl\temp_combined.mp4
2025-07-28 20:31:24,686 - INFO - 合并后的视频时长: 4.01秒，目标音频时长: 2.50秒
2025-07-28 20:31:24,687 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnreq3frl\temp_combined.mp4 -ss 0 -to 2.505 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-28 20:31:24,931 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:24,931 - INFO - 目标音频时长: 2.50秒
2025-07-28 20:31:24,931 - INFO - 实际视频时长: 2.54秒
2025-07-28 20:31:24,931 - INFO - 时长差异: 0.04秒 (1.52%)
2025-07-28 20:31:24,931 - INFO - ==========================================
2025-07-28 20:31:24,931 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:24,931 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-28 20:31:24,931 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnreq3frl
2025-07-28 20:31:24,973 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:24,973 - INFO -   - 音频时长: 2.50秒
2025-07-28 20:31:24,973 - INFO -   - 视频时长: 2.54秒
2025-07-28 20:31:24,973 - INFO -   - 时长差异: 0.04秒 (1.52%)
2025-07-28 20:31:24,973 - INFO - 
字幕 #30 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:24,973 - INFO - 生成的视频文件:
2025-07-28 20:31:24,973 - INFO -   1. F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-07-28 20:31:24,973 - INFO -   2. F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-07-28 20:31:24,973 - INFO - ========== 字幕 #30 处理结束 ==========

