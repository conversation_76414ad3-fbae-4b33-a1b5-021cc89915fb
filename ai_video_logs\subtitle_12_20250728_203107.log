2025-07-28 20:31:07,646 - INFO - ========== 字幕 #12 处理开始 ==========
2025-07-28 20:31:07,647 - INFO - 字幕内容: 面对猎户大叔的好意，女孩婉言谢绝，她要用实力证明自己。
2025-07-28 20:31:07,647 - INFO - 字幕序号: [922, 923]
2025-07-28 20:31:07,647 - INFO - 音频文件详情:
2025-07-28 20:31:07,647 - INFO -   - 路径: output\12.wav
2025-07-28 20:31:07,647 - INFO -   - 时长: 2.73秒
2025-07-28 20:31:07,647 - INFO -   - 验证音频时长: 2.73秒
2025-07-28 20:31:07,647 - INFO - 字幕时间戳信息:
2025-07-28 20:31:07,647 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:07,647 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:07,647 - INFO -   - 根据生成的音频时长(2.73秒)已调整字幕时间戳
2025-07-28 20:31:07,647 - INFO - ========== 开始为字幕 #12 生成 6 套场景方案 ==========
2025-07-28 20:31:07,647 - INFO - 开始查找字幕序号 [922, 923] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:07,648 - INFO - 找到related_overlap场景: scene_id=978, 字幕#922
2025-07-28 20:31:07,648 - INFO - 找到related_overlap场景: scene_id=979, 字幕#923
2025-07-28 20:31:07,648 - INFO - 找到related_between场景: scene_id=975, 字幕#922
2025-07-28 20:31:07,648 - INFO - 找到related_between场景: scene_id=976, 字幕#922
2025-07-28 20:31:07,648 - INFO - 找到related_between场景: scene_id=977, 字幕#922
2025-07-28 20:31:07,648 - INFO - 找到related_between场景: scene_id=980, 字幕#923
2025-07-28 20:31:07,649 - INFO - 字幕 #922 找到 1 个overlap场景, 3 个between场景
2025-07-28 20:31:07,649 - INFO - 字幕 #923 找到 1 个overlap场景, 1 个between场景
2025-07-28 20:31:07,649 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-07-28 20:31:07,649 - INFO - 开始生成方案 #1
2025-07-28 20:31:07,649 - INFO - 方案 #1: 为字幕#922选择初始化overlap场景id=978
2025-07-28 20:31:07,649 - INFO - 方案 #1: 为字幕#923选择初始化overlap场景id=979
2025-07-28 20:31:07,649 - INFO - 方案 #1: 初始选择后，当前总时长=2.28秒
2025-07-28 20:31:07,649 - INFO - 方案 #1: 额外between选择后，当前总时长=2.28秒
2025-07-28 20:31:07,649 - INFO - 方案 #1: 额外添加between场景id=977, 当前总时长=2.48秒
2025-07-28 20:31:07,649 - INFO - 方案 #1: 额外添加between场景id=976, 当前总时长=4.80秒
2025-07-28 20:31:07,649 - INFO - 方案 #1: 场景总时长(4.80秒)大于音频时长(2.73秒)，需要裁剪
2025-07-28 20:31:07,649 - INFO - 调整前总时长: 4.80秒, 目标时长: 2.73秒
2025-07-28 20:31:07,649 - INFO - 需要裁剪 2.07秒
2025-07-28 20:31:07,649 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:07,649 - INFO - 裁剪场景ID=976：从2.32秒裁剪至1.00秒
2025-07-28 20:31:07,649 - INFO - 裁剪场景ID=978：从2.12秒裁剪至1.37秒
2025-07-28 20:31:07,649 - INFO - 调整后总时长: 2.73秒，与目标时长差异: 0.00秒
2025-07-28 20:31:07,649 - INFO - 方案 #1 调整/填充后最终总时长: 2.73秒
2025-07-28 20:31:07,649 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:07,649 - INFO - 开始生成方案 #2
2025-07-28 20:31:07,649 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:07,649 - INFO - 方案 #2: 为字幕#922选择初始化between场景id=975
2025-07-28 20:31:07,649 - INFO - 方案 #2: 为字幕#923选择初始化between场景id=980
2025-07-28 20:31:07,649 - INFO - 方案 #2: 额外between选择后，当前总时长=3.48秒
2025-07-28 20:31:07,649 - INFO - 方案 #2: 场景总时长(3.48秒)大于音频时长(2.73秒)，需要裁剪
2025-07-28 20:31:07,649 - INFO - 调整前总时长: 3.48秒, 目标时长: 2.73秒
2025-07-28 20:31:07,649 - INFO - 需要裁剪 0.75秒
2025-07-28 20:31:07,649 - INFO - 裁剪最长场景ID=975：从2.24秒裁剪至1.49秒
2025-07-28 20:31:07,649 - INFO - 调整后总时长: 2.73秒，与目标时长差异: 0.00秒
2025-07-28 20:31:07,650 - INFO - 方案 #2 调整/填充后最终总时长: 2.73秒
2025-07-28 20:31:07,650 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:07,650 - INFO - 开始生成方案 #3
2025-07-28 20:31:07,650 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:07,650 - INFO - 开始生成方案 #4
2025-07-28 20:31:07,650 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:07,650 - INFO - 开始生成方案 #5
2025-07-28 20:31:07,650 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:07,650 - INFO - 开始生成方案 #6
2025-07-28 20:31:07,650 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:07,650 - INFO - ========== 字幕 #12 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:07,650 - INFO - 
----- 处理字幕 #12 的方案 #1 -----
2025-07-28 20:31:07,650 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-28 20:31:07,650 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps_kq987a
2025-07-28 20:31:07,651 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\978.mp4 (确认存在: True)
2025-07-28 20:31:07,651 - INFO - 添加场景ID=978，时长=2.12秒，累计时长=2.12秒
2025-07-28 20:31:07,651 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\979.mp4 (确认存在: True)
2025-07-28 20:31:07,651 - INFO - 添加场景ID=979，时长=0.16秒，累计时长=2.28秒
2025-07-28 20:31:07,651 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\977.mp4 (确认存在: True)
2025-07-28 20:31:07,651 - INFO - 添加场景ID=977，时长=0.20秒，累计时长=2.48秒
2025-07-28 20:31:07,651 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\976.mp4 (确认存在: True)
2025-07-28 20:31:07,651 - INFO - 添加场景ID=976，时长=2.32秒，累计时长=4.80秒
2025-07-28 20:31:07,651 - INFO - 场景总时长(4.80秒)已达到音频时长(2.73秒)的1.5倍，停止添加场景
2025-07-28 20:31:07,651 - INFO - 准备合并 4 个场景文件，总时长约 4.80秒
2025-07-28 20:31:07,651 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/978.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/979.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/977.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/976.mp4'

2025-07-28 20:31:07,651 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps_kq987a\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps_kq987a\temp_combined.mp4
2025-07-28 20:31:07,861 - INFO - 合并后的视频时长: 4.89秒，目标音频时长: 2.73秒
2025-07-28 20:31:07,861 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps_kq987a\temp_combined.mp4 -ss 0 -to 2.731 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-28 20:31:08,143 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:08,143 - INFO - 目标音频时长: 2.73秒
2025-07-28 20:31:08,143 - INFO - 实际视频时长: 2.78秒
2025-07-28 20:31:08,143 - INFO - 时长差异: 0.05秒 (1.90%)
2025-07-28 20:31:08,143 - INFO - ==========================================
2025-07-28 20:31:08,143 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:08,143 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-28 20:31:08,144 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps_kq987a
2025-07-28 20:31:08,187 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:08,187 - INFO -   - 音频时长: 2.73秒
2025-07-28 20:31:08,187 - INFO -   - 视频时长: 2.78秒
2025-07-28 20:31:08,187 - INFO -   - 时长差异: 0.05秒 (1.90%)
2025-07-28 20:31:08,187 - INFO - 
----- 处理字幕 #12 的方案 #2 -----
2025-07-28 20:31:08,187 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-28 20:31:08,187 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphu4o5w5a
2025-07-28 20:31:08,187 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\975.mp4 (确认存在: True)
2025-07-28 20:31:08,187 - INFO - 添加场景ID=975，时长=2.24秒，累计时长=2.24秒
2025-07-28 20:31:08,188 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\980.mp4 (确认存在: True)
2025-07-28 20:31:08,188 - INFO - 添加场景ID=980，时长=1.24秒，累计时长=3.48秒
2025-07-28 20:31:08,188 - INFO - 准备合并 2 个场景文件，总时长约 3.48秒
2025-07-28 20:31:08,188 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/975.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/980.mp4'

2025-07-28 20:31:08,188 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphu4o5w5a\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphu4o5w5a\temp_combined.mp4
2025-07-28 20:31:08,309 - INFO - 合并后的视频时长: 3.53秒，目标音频时长: 2.73秒
2025-07-28 20:31:08,309 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphu4o5w5a\temp_combined.mp4 -ss 0 -to 2.731 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-28 20:31:08,530 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:08,530 - INFO - 目标音频时长: 2.73秒
2025-07-28 20:31:08,530 - INFO - 实际视频时长: 2.78秒
2025-07-28 20:31:08,530 - INFO - 时长差异: 0.05秒 (1.90%)
2025-07-28 20:31:08,530 - INFO - ==========================================
2025-07-28 20:31:08,530 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:08,530 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-28 20:31:08,530 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphu4o5w5a
2025-07-28 20:31:08,569 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:08,569 - INFO -   - 音频时长: 2.73秒
2025-07-28 20:31:08,569 - INFO -   - 视频时长: 2.78秒
2025-07-28 20:31:08,569 - INFO -   - 时长差异: 0.05秒 (1.90%)
2025-07-28 20:31:08,570 - INFO - 
字幕 #12 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:08,570 - INFO - 生成的视频文件:
2025-07-28 20:31:08,570 - INFO -   1. F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-07-28 20:31:08,570 - INFO -   2. F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-07-28 20:31:08,570 - INFO - ========== 字幕 #12 处理结束 ==========

