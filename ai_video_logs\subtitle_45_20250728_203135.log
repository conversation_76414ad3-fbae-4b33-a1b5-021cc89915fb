2025-07-28 20:31:35,427 - INFO - ========== 字幕 #45 处理开始 ==========
2025-07-28 20:31:35,428 - INFO - 字幕内容: 原来，敌国正派大力士前来挑衅，朝中无人能敌，太子此行正是为了寻求能人帮助。
2025-07-28 20:31:35,428 - INFO - 字幕序号: [3320, 3323]
2025-07-28 20:31:35,428 - INFO - 音频文件详情:
2025-07-28 20:31:35,428 - INFO -   - 路径: output\45.wav
2025-07-28 20:31:35,428 - INFO -   - 时长: 4.16秒
2025-07-28 20:31:35,428 - INFO -   - 验证音频时长: 4.16秒
2025-07-28 20:31:35,428 - INFO - 字幕时间戳信息:
2025-07-28 20:31:35,428 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:35,428 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:35,428 - INFO -   - 根据生成的音频时长(4.16秒)已调整字幕时间戳
2025-07-28 20:31:35,428 - INFO - ========== 开始为字幕 #45 生成 6 套场景方案 ==========
2025-07-28 20:31:35,428 - INFO - 开始查找字幕序号 [3320, 3323] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:35,429 - INFO - 找到related_overlap场景: scene_id=3491, 字幕#3320
2025-07-28 20:31:35,429 - INFO - 找到related_overlap场景: scene_id=3492, 字幕#3320
2025-07-28 20:31:35,429 - INFO - 找到related_overlap场景: scene_id=3493, 字幕#3323
2025-07-28 20:31:35,430 - INFO - 字幕 #3320 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:35,430 - INFO - 字幕 #3323 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:35,430 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:35,430 - INFO - 开始生成方案 #1
2025-07-28 20:31:35,430 - INFO - 方案 #1: 为字幕#3320选择初始化overlap场景id=3491
2025-07-28 20:31:35,430 - INFO - 方案 #1: 为字幕#3323选择初始化overlap场景id=3493
2025-07-28 20:31:35,430 - INFO - 方案 #1: 初始选择后，当前总时长=3.52秒
2025-07-28 20:31:35,430 - INFO - 方案 #1: 额外添加overlap场景id=3492, 当前总时长=5.52秒
2025-07-28 20:31:35,430 - INFO - 方案 #1: 额外between选择后，当前总时长=5.52秒
2025-07-28 20:31:35,430 - INFO - 方案 #1: 场景总时长(5.52秒)大于音频时长(4.16秒)，需要裁剪
2025-07-28 20:31:35,430 - INFO - 调整前总时长: 5.52秒, 目标时长: 4.16秒
2025-07-28 20:31:35,430 - INFO - 需要裁剪 1.36秒
2025-07-28 20:31:35,430 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:35,430 - INFO - 裁剪场景ID=3492：从2.00秒裁剪至1.00秒
2025-07-28 20:31:35,430 - INFO - 裁剪场景ID=3491：从1.84秒裁剪至1.48秒
2025-07-28 20:31:35,430 - INFO - 调整后总时长: 4.16秒，与目标时长差异: 0.00秒
2025-07-28 20:31:35,430 - INFO - 方案 #1 调整/填充后最终总时长: 4.16秒
2025-07-28 20:31:35,430 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:35,430 - INFO - 开始生成方案 #2
2025-07-28 20:31:35,430 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,430 - INFO - 开始生成方案 #3
2025-07-28 20:31:35,430 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,430 - INFO - 开始生成方案 #4
2025-07-28 20:31:35,430 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,430 - INFO - 开始生成方案 #5
2025-07-28 20:31:35,430 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,430 - INFO - 开始生成方案 #6
2025-07-28 20:31:35,430 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:35,430 - INFO - ========== 字幕 #45 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:35,430 - INFO - 
----- 处理字幕 #45 的方案 #1 -----
2025-07-28 20:31:35,430 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-28 20:31:35,431 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzxoevupy
2025-07-28 20:31:35,431 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3491.mp4 (确认存在: True)
2025-07-28 20:31:35,431 - INFO - 添加场景ID=3491，时长=1.84秒，累计时长=1.84秒
2025-07-28 20:31:35,431 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3493.mp4 (确认存在: True)
2025-07-28 20:31:35,431 - INFO - 添加场景ID=3493，时长=1.68秒，累计时长=3.52秒
2025-07-28 20:31:35,431 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3492.mp4 (确认存在: True)
2025-07-28 20:31:35,431 - INFO - 添加场景ID=3492，时长=2.00秒，累计时长=5.52秒
2025-07-28 20:31:35,431 - INFO - 准备合并 3 个场景文件，总时长约 5.52秒
2025-07-28 20:31:35,431 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3491.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3493.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3492.mp4'

2025-07-28 20:31:35,431 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzxoevupy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzxoevupy\temp_combined.mp4
2025-07-28 20:31:35,589 - INFO - 合并后的视频时长: 5.59秒，目标音频时长: 4.16秒
2025-07-28 20:31:35,589 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzxoevupy\temp_combined.mp4 -ss 0 -to 4.163 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-28 20:31:35,922 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:35,922 - INFO - 目标音频时长: 4.16秒
2025-07-28 20:31:35,922 - INFO - 实际视频时长: 4.22秒
2025-07-28 20:31:35,922 - INFO - 时长差异: 0.06秒 (1.44%)
2025-07-28 20:31:35,922 - INFO - ==========================================
2025-07-28 20:31:35,922 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:35,922 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-28 20:31:35,923 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzxoevupy
2025-07-28 20:31:35,967 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:35,967 - INFO -   - 音频时长: 4.16秒
2025-07-28 20:31:35,967 - INFO -   - 视频时长: 4.22秒
2025-07-28 20:31:35,967 - INFO -   - 时长差异: 0.06秒 (1.44%)
2025-07-28 20:31:35,967 - INFO - 
字幕 #45 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:35,967 - INFO - 生成的视频文件:
2025-07-28 20:31:35,967 - INFO -   1. F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-07-28 20:31:35,967 - INFO - ========== 字幕 #45 处理结束 ==========

