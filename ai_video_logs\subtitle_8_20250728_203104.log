2025-07-28 20:31:04,719 - INFO - ========== 字幕 #8 处理开始 ==========
2025-07-28 20:31:04,719 - INFO - 字幕内容: 在绝对的力量面前，她终于交出了救命钱，女孩的父亲得救了。
2025-07-28 20:31:04,719 - INFO - 字幕序号: [211, 213]
2025-07-28 20:31:04,719 - INFO - 音频文件详情:
2025-07-28 20:31:04,719 - INFO -   - 路径: output\8.wav
2025-07-28 20:31:04,719 - INFO -   - 时长: 3.12秒
2025-07-28 20:31:04,720 - INFO -   - 验证音频时长: 3.12秒
2025-07-28 20:31:04,720 - INFO - 字幕时间戳信息:
2025-07-28 20:31:04,720 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:04,720 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:04,720 - INFO -   - 根据生成的音频时长(3.12秒)已调整字幕时间戳
2025-07-28 20:31:04,720 - INFO - ========== 开始为字幕 #8 生成 6 套场景方案 ==========
2025-07-28 20:31:04,720 - INFO - 开始查找字幕序号 [211, 213] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:04,720 - INFO - 找到related_overlap场景: scene_id=251, 字幕#211
2025-07-28 20:31:04,720 - INFO - 找到related_overlap场景: scene_id=255, 字幕#213
2025-07-28 20:31:04,720 - INFO - 找到related_overlap场景: scene_id=256, 字幕#213
2025-07-28 20:31:04,721 - INFO - 找到related_between场景: scene_id=254, 字幕#213
2025-07-28 20:31:04,721 - INFO - 字幕 #211 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:04,721 - INFO - 字幕 #213 找到 2 个overlap场景, 1 个between场景
2025-07-28 20:31:04,721 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 20:31:04,722 - INFO - 开始生成方案 #1
2025-07-28 20:31:04,722 - INFO - 方案 #1: 为字幕#211选择初始化overlap场景id=251
2025-07-28 20:31:04,722 - INFO - 方案 #1: 为字幕#213选择初始化overlap场景id=255
2025-07-28 20:31:04,722 - INFO - 方案 #1: 初始选择后，当前总时长=3.88秒
2025-07-28 20:31:04,722 - INFO - 方案 #1: 额外between选择后，当前总时长=3.88秒
2025-07-28 20:31:04,722 - INFO - 方案 #1: 场景总时长(3.88秒)大于音频时长(3.12秒)，需要裁剪
2025-07-28 20:31:04,722 - INFO - 调整前总时长: 3.88秒, 目标时长: 3.12秒
2025-07-28 20:31:04,722 - INFO - 需要裁剪 0.76秒
2025-07-28 20:31:04,722 - INFO - 裁剪最长场景ID=255：从2.20秒裁剪至1.44秒
2025-07-28 20:31:04,722 - INFO - 调整后总时长: 3.12秒，与目标时长差异: 0.00秒
2025-07-28 20:31:04,722 - INFO - 方案 #1 调整/填充后最终总时长: 3.12秒
2025-07-28 20:31:04,722 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:04,722 - INFO - 开始生成方案 #2
2025-07-28 20:31:04,722 - INFO - 方案 #2: 为字幕#213选择初始化overlap场景id=256
2025-07-28 20:31:04,722 - INFO - 方案 #2: 初始选择后，当前总时长=2.24秒
2025-07-28 20:31:04,722 - INFO - 方案 #2: 额外between选择后，当前总时长=2.24秒
2025-07-28 20:31:04,722 - INFO - 方案 #2: 额外添加between场景id=254, 当前总时长=2.96秒
2025-07-28 20:31:04,722 - INFO - 方案 #2: 场景总时长(2.96秒)小于音频时长(3.12秒)，需要延伸填充
2025-07-28 20:31:04,722 - INFO - 方案 #2: 最后一个场景ID: 254
2025-07-28 20:31:04,722 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 253
2025-07-28 20:31:04,722 - INFO - 方案 #2: 需要填充时长: 0.16秒
2025-07-28 20:31:04,722 - INFO - 方案 #2: 跳过已使用的场景: scene_id=255
2025-07-28 20:31:04,722 - INFO - 方案 #2: 跳过已使用的场景: scene_id=256
2025-07-28 20:31:04,722 - INFO - 方案 #2: 追加场景 scene_id=257 (裁剪至 0.16秒)
2025-07-28 20:31:04,722 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:04,722 - INFO - 方案 #2 调整/填充后最终总时长: 3.12秒
2025-07-28 20:31:04,722 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:04,722 - INFO - 开始生成方案 #3
2025-07-28 20:31:04,722 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:04,722 - INFO - 开始生成方案 #4
2025-07-28 20:31:04,722 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:04,722 - INFO - 开始生成方案 #5
2025-07-28 20:31:04,722 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:04,722 - INFO - 开始生成方案 #6
2025-07-28 20:31:04,722 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:04,722 - INFO - ========== 字幕 #8 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:04,722 - INFO - 
----- 处理字幕 #8 的方案 #1 -----
2025-07-28 20:31:04,722 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-28 20:31:04,723 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk9ozp3y5
2025-07-28 20:31:04,723 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\251.mp4 (确认存在: True)
2025-07-28 20:31:04,723 - INFO - 添加场景ID=251，时长=1.68秒，累计时长=1.68秒
2025-07-28 20:31:04,723 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\255.mp4 (确认存在: True)
2025-07-28 20:31:04,723 - INFO - 添加场景ID=255，时长=2.20秒，累计时长=3.88秒
2025-07-28 20:31:04,723 - INFO - 准备合并 2 个场景文件，总时长约 3.88秒
2025-07-28 20:31:04,723 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/251.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/255.mp4'

2025-07-28 20:31:04,723 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpk9ozp3y5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpk9ozp3y5\temp_combined.mp4
2025-07-28 20:31:04,852 - INFO - 合并后的视频时长: 3.93秒，目标音频时长: 3.12秒
2025-07-28 20:31:04,852 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpk9ozp3y5\temp_combined.mp4 -ss 0 -to 3.12 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-28 20:31:05,098 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:05,098 - INFO - 目标音频时长: 3.12秒
2025-07-28 20:31:05,098 - INFO - 实际视频时长: 3.14秒
2025-07-28 20:31:05,098 - INFO - 时长差异: 0.02秒 (0.74%)
2025-07-28 20:31:05,098 - INFO - ==========================================
2025-07-28 20:31:05,098 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:05,098 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-28 20:31:05,099 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk9ozp3y5
2025-07-28 20:31:05,139 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:05,139 - INFO -   - 音频时长: 3.12秒
2025-07-28 20:31:05,139 - INFO -   - 视频时长: 3.14秒
2025-07-28 20:31:05,139 - INFO -   - 时长差异: 0.02秒 (0.74%)
2025-07-28 20:31:05,139 - INFO - 
----- 处理字幕 #8 的方案 #2 -----
2025-07-28 20:31:05,139 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-28 20:31:05,140 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8wb171wm
2025-07-28 20:31:05,140 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\256.mp4 (确认存在: True)
2025-07-28 20:31:05,140 - INFO - 添加场景ID=256，时长=2.24秒，累计时长=2.24秒
2025-07-28 20:31:05,140 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\254.mp4 (确认存在: True)
2025-07-28 20:31:05,140 - INFO - 添加场景ID=254，时长=0.72秒，累计时长=2.96秒
2025-07-28 20:31:05,140 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\257.mp4 (确认存在: True)
2025-07-28 20:31:05,140 - INFO - 添加场景ID=257，时长=1.00秒，累计时长=3.96秒
2025-07-28 20:31:05,141 - INFO - 准备合并 3 个场景文件，总时长约 3.96秒
2025-07-28 20:31:05,141 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/256.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/254.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/257.mp4'

2025-07-28 20:31:05,141 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8wb171wm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8wb171wm\temp_combined.mp4
2025-07-28 20:31:05,288 - INFO - 合并后的视频时长: 4.03秒，目标音频时长: 3.12秒
2025-07-28 20:31:05,288 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8wb171wm\temp_combined.mp4 -ss 0 -to 3.12 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-28 20:31:05,528 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:05,528 - INFO - 目标音频时长: 3.12秒
2025-07-28 20:31:05,528 - INFO - 实际视频时长: 3.14秒
2025-07-28 20:31:05,528 - INFO - 时长差异: 0.02秒 (0.74%)
2025-07-28 20:31:05,528 - INFO - ==========================================
2025-07-28 20:31:05,528 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:05,529 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-28 20:31:05,529 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8wb171wm
2025-07-28 20:31:05,576 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:05,576 - INFO -   - 音频时长: 3.12秒
2025-07-28 20:31:05,576 - INFO -   - 视频时长: 3.14秒
2025-07-28 20:31:05,576 - INFO -   - 时长差异: 0.02秒 (0.74%)
2025-07-28 20:31:05,576 - INFO - 
字幕 #8 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:05,576 - INFO - 生成的视频文件:
2025-07-28 20:31:05,576 - INFO -   1. F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-07-28 20:31:05,576 - INFO -   2. F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-07-28 20:31:05,576 - INFO - ========== 字幕 #8 处理结束 ==========

