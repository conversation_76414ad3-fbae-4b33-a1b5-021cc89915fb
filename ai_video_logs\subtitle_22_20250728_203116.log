2025-07-28 20:31:16,026 - INFO - ========== 字幕 #22 处理开始 ==========
2025-07-28 20:31:16,026 - INFO - 字幕内容: 夜幕降临，巨大的熊瞎子真的出现了，村民们瞬间陷入恐慌。
2025-07-28 20:31:16,026 - INFO - 字幕序号: [1922, 1923]
2025-07-28 20:31:16,026 - INFO - 音频文件详情:
2025-07-28 20:31:16,026 - INFO -   - 路径: output\22.wav
2025-07-28 20:31:16,026 - INFO -   - 时长: 2.99秒
2025-07-28 20:31:16,027 - INFO -   - 验证音频时长: 2.99秒
2025-07-28 20:31:16,027 - INFO - 字幕时间戳信息:
2025-07-28 20:31:16,027 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:16,027 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:16,027 - INFO -   - 根据生成的音频时长(2.99秒)已调整字幕时间戳
2025-07-28 20:31:16,027 - INFO - ========== 开始为字幕 #22 生成 6 套场景方案 ==========
2025-07-28 20:31:16,027 - INFO - 开始查找字幕序号 [1922, 1923] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:16,028 - INFO - 找到related_overlap场景: scene_id=1982, 字幕#1922
2025-07-28 20:31:16,028 - INFO - 找到related_overlap场景: scene_id=1983, 字幕#1923
2025-07-28 20:31:16,028 - INFO - 找到related_overlap场景: scene_id=1984, 字幕#1923
2025-07-28 20:31:16,029 - INFO - 找到related_between场景: scene_id=1979, 字幕#1922
2025-07-28 20:31:16,029 - INFO - 找到related_between场景: scene_id=1980, 字幕#1922
2025-07-28 20:31:16,029 - INFO - 找到related_between场景: scene_id=1981, 字幕#1922
2025-07-28 20:31:16,029 - INFO - 找到related_between场景: scene_id=1985, 字幕#1923
2025-07-28 20:31:16,029 - INFO - 找到related_between场景: scene_id=1986, 字幕#1923
2025-07-28 20:31:16,029 - INFO - 找到related_between场景: scene_id=1987, 字幕#1923
2025-07-28 20:31:16,030 - INFO - 字幕 #1922 找到 1 个overlap场景, 3 个between场景
2025-07-28 20:31:16,030 - INFO - 字幕 #1923 找到 2 个overlap场景, 3 个between场景
2025-07-28 20:31:16,030 - INFO - 共收集 3 个未使用的overlap场景和 6 个未使用的between场景
2025-07-28 20:31:16,030 - INFO - 开始生成方案 #1
2025-07-28 20:31:16,030 - INFO - 方案 #1: 为字幕#1922选择初始化overlap场景id=1982
2025-07-28 20:31:16,030 - INFO - 方案 #1: 为字幕#1923选择初始化overlap场景id=1984
2025-07-28 20:31:16,030 - INFO - 方案 #1: 初始选择后，当前总时长=1.80秒
2025-07-28 20:31:16,030 - INFO - 方案 #1: 额外添加overlap场景id=1983, 当前总时长=2.60秒
2025-07-28 20:31:16,030 - INFO - 方案 #1: 额外between选择后，当前总时长=2.60秒
2025-07-28 20:31:16,030 - INFO - 方案 #1: 额外添加between场景id=1981, 当前总时长=3.48秒
2025-07-28 20:31:16,030 - INFO - 方案 #1: 场景总时长(3.48秒)大于音频时长(2.99秒)，需要裁剪
2025-07-28 20:31:16,030 - INFO - 调整前总时长: 3.48秒, 目标时长: 2.99秒
2025-07-28 20:31:16,030 - INFO - 需要裁剪 0.49秒
2025-07-28 20:31:16,030 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:16,030 - INFO - 裁剪场景ID=1982：从1.32秒裁剪至1.00秒
2025-07-28 20:31:16,030 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.17秒
2025-07-28 20:31:16,030 - INFO - 移除场景ID=1984，时长=0.48秒
2025-07-28 20:31:16,030 - INFO - 调整后总时长: 2.68秒，与目标时长差异: 0.31秒
2025-07-28 20:31:16,030 - INFO - 方案 #1 调整/填充后最终总时长: 2.68秒
2025-07-28 20:31:16,030 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:16,030 - INFO - 开始生成方案 #2
2025-07-28 20:31:16,030 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:16,030 - INFO - 方案 #2: 为字幕#1922选择初始化between场景id=1979
2025-07-28 20:31:16,030 - INFO - 方案 #2: 为字幕#1923选择初始化between场景id=1985
2025-07-28 20:31:16,030 - INFO - 方案 #2: 额外between选择后，当前总时长=2.12秒
2025-07-28 20:31:16,030 - INFO - 方案 #2: 额外添加between场景id=1987, 当前总时长=4.44秒
2025-07-28 20:31:16,030 - INFO - 方案 #2: 场景总时长(4.44秒)大于音频时长(2.99秒)，需要裁剪
2025-07-28 20:31:16,030 - INFO - 调整前总时长: 4.44秒, 目标时长: 2.99秒
2025-07-28 20:31:16,030 - INFO - 需要裁剪 1.45秒
2025-07-28 20:31:16,030 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:16,030 - INFO - 裁剪场景ID=1987：从2.32秒裁剪至1.00秒
2025-07-28 20:31:16,030 - INFO - 裁剪场景ID=1985：从1.40秒裁剪至1.27秒
2025-07-28 20:31:16,030 - INFO - 调整后总时长: 2.99秒，与目标时长差异: 0.00秒
2025-07-28 20:31:16,030 - INFO - 方案 #2 调整/填充后最终总时长: 2.99秒
2025-07-28 20:31:16,030 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:16,030 - INFO - 开始生成方案 #3
2025-07-28 20:31:16,030 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:16,030 - INFO - 方案 #3: 为字幕#1922选择初始化between场景id=1980
2025-07-28 20:31:16,030 - INFO - 方案 #3: 为字幕#1923选择初始化between场景id=1986
2025-07-28 20:31:16,030 - INFO - 方案 #3: 额外between选择后，当前总时长=2.28秒
2025-07-28 20:31:16,030 - INFO - 方案 #3: 场景总时长(2.28秒)小于音频时长(2.99秒)，需要延伸填充
2025-07-28 20:31:16,030 - INFO - 方案 #3: 最后一个场景ID: 1986
2025-07-28 20:31:16,030 - INFO - 方案 #3: 找到最后一个场景在原始列表中的索引: 1985
2025-07-28 20:31:16,030 - INFO - 方案 #3: 需要填充时长: 0.71秒
2025-07-28 20:31:16,030 - INFO - 方案 #3: 跳过已使用的场景: scene_id=1987
2025-07-28 20:31:16,030 - INFO - 方案 #3: 追加场景 scene_id=1988 (裁剪至 0.71秒)
2025-07-28 20:31:16,030 - INFO - 方案 #3: 成功填充至目标时长
2025-07-28 20:31:16,031 - INFO - 方案 #3 调整/填充后最终总时长: 2.99秒
2025-07-28 20:31:16,031 - INFO - 方案 #3 添加到方案列表
2025-07-28 20:31:16,031 - INFO - 开始生成方案 #4
2025-07-28 20:31:16,031 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:16,031 - INFO - 开始生成方案 #5
2025-07-28 20:31:16,031 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:16,031 - INFO - 开始生成方案 #6
2025-07-28 20:31:16,031 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:16,031 - INFO - ========== 字幕 #22 的 3 套有效场景方案生成完成 ==========
2025-07-28 20:31:16,031 - INFO - 
----- 处理字幕 #22 的方案 #1 -----
2025-07-28 20:31:16,031 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-28 20:31:16,031 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpslosazl3
2025-07-28 20:31:16,032 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1982.mp4 (确认存在: True)
2025-07-28 20:31:16,032 - INFO - 添加场景ID=1982，时长=1.32秒，累计时长=1.32秒
2025-07-28 20:31:16,032 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1983.mp4 (确认存在: True)
2025-07-28 20:31:16,032 - INFO - 添加场景ID=1983，时长=0.80秒，累计时长=2.12秒
2025-07-28 20:31:16,032 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1981.mp4 (确认存在: True)
2025-07-28 20:31:16,032 - INFO - 添加场景ID=1981，时长=0.88秒，累计时长=3.00秒
2025-07-28 20:31:16,032 - INFO - 准备合并 3 个场景文件，总时长约 3.00秒
2025-07-28 20:31:16,032 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1982.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1983.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1981.mp4'

2025-07-28 20:31:16,032 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpslosazl3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpslosazl3\temp_combined.mp4
2025-07-28 20:31:16,165 - INFO - 合并后的视频时长: 3.07秒，目标音频时长: 2.99秒
2025-07-28 20:31:16,165 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpslosazl3\temp_combined.mp4 -ss 0 -to 2.989 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-28 20:31:16,447 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:16,447 - INFO - 目标音频时长: 2.99秒
2025-07-28 20:31:16,447 - INFO - 实际视频时长: 3.02秒
2025-07-28 20:31:16,447 - INFO - 时长差异: 0.03秒 (1.14%)
2025-07-28 20:31:16,447 - INFO - ==========================================
2025-07-28 20:31:16,447 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:16,447 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-28 20:31:16,447 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpslosazl3
2025-07-28 20:31:16,486 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:16,486 - INFO -   - 音频时长: 2.99秒
2025-07-28 20:31:16,486 - INFO -   - 视频时长: 3.02秒
2025-07-28 20:31:16,486 - INFO -   - 时长差异: 0.03秒 (1.14%)
2025-07-28 20:31:16,486 - INFO - 
----- 处理字幕 #22 的方案 #2 -----
2025-07-28 20:31:16,486 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-28 20:31:16,486 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv4o7998p
2025-07-28 20:31:16,487 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1979.mp4 (确认存在: True)
2025-07-28 20:31:16,487 - INFO - 添加场景ID=1979，时长=0.72秒，累计时长=0.72秒
2025-07-28 20:31:16,487 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1985.mp4 (确认存在: True)
2025-07-28 20:31:16,487 - INFO - 添加场景ID=1985，时长=1.40秒，累计时长=2.12秒
2025-07-28 20:31:16,487 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1987.mp4 (确认存在: True)
2025-07-28 20:31:16,487 - INFO - 添加场景ID=1987，时长=2.32秒，累计时长=4.44秒
2025-07-28 20:31:16,487 - INFO - 准备合并 3 个场景文件，总时长约 4.44秒
2025-07-28 20:31:16,487 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1979.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1985.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1987.mp4'

2025-07-28 20:31:16,487 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpv4o7998p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpv4o7998p\temp_combined.mp4
2025-07-28 20:31:16,624 - INFO - 合并后的视频时长: 4.51秒，目标音频时长: 2.99秒
2025-07-28 20:31:16,624 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpv4o7998p\temp_combined.mp4 -ss 0 -to 2.989 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-28 20:31:16,889 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:16,889 - INFO - 目标音频时长: 2.99秒
2025-07-28 20:31:16,889 - INFO - 实际视频时长: 3.02秒
2025-07-28 20:31:16,889 - INFO - 时长差异: 0.03秒 (1.14%)
2025-07-28 20:31:16,889 - INFO - ==========================================
2025-07-28 20:31:16,889 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:16,889 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-28 20:31:16,890 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv4o7998p
2025-07-28 20:31:16,934 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:16,934 - INFO -   - 音频时长: 2.99秒
2025-07-28 20:31:16,934 - INFO -   - 视频时长: 3.02秒
2025-07-28 20:31:16,934 - INFO -   - 时长差异: 0.03秒 (1.14%)
2025-07-28 20:31:16,934 - INFO - 
----- 处理字幕 #22 的方案 #3 -----
2025-07-28 20:31:16,934 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-28 20:31:16,935 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpoilvqojq
2025-07-28 20:31:16,935 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1980.mp4 (确认存在: True)
2025-07-28 20:31:16,935 - INFO - 添加场景ID=1980，时长=0.96秒，累计时长=0.96秒
2025-07-28 20:31:16,935 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1986.mp4 (确认存在: True)
2025-07-28 20:31:16,935 - INFO - 添加场景ID=1986，时长=1.32秒，累计时长=2.28秒
2025-07-28 20:31:16,935 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1988.mp4 (确认存在: True)
2025-07-28 20:31:16,935 - INFO - 添加场景ID=1988，时长=2.12秒，累计时长=4.40秒
2025-07-28 20:31:16,935 - INFO - 准备合并 3 个场景文件，总时长约 4.40秒
2025-07-28 20:31:16,935 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1980.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1986.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1988.mp4'

2025-07-28 20:31:16,936 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpoilvqojq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpoilvqojq\temp_combined.mp4
2025-07-28 20:31:17,074 - INFO - 合并后的视频时长: 4.47秒，目标音频时长: 2.99秒
2025-07-28 20:31:17,075 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpoilvqojq\temp_combined.mp4 -ss 0 -to 2.989 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-28 20:31:17,338 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:17,338 - INFO - 目标音频时长: 2.99秒
2025-07-28 20:31:17,338 - INFO - 实际视频时长: 3.02秒
2025-07-28 20:31:17,338 - INFO - 时长差异: 0.03秒 (1.14%)
2025-07-28 20:31:17,338 - INFO - ==========================================
2025-07-28 20:31:17,338 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:17,338 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-28 20:31:17,338 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpoilvqojq
2025-07-28 20:31:17,378 - INFO - 方案 #3 处理完成:
2025-07-28 20:31:17,378 - INFO -   - 音频时长: 2.99秒
2025-07-28 20:31:17,378 - INFO -   - 视频时长: 3.02秒
2025-07-28 20:31:17,378 - INFO -   - 时长差异: 0.03秒 (1.14%)
2025-07-28 20:31:17,378 - INFO - 
字幕 #22 处理完成，成功生成 3/3 套方案
2025-07-28 20:31:17,378 - INFO - 生成的视频文件:
2025-07-28 20:31:17,378 - INFO -   1. F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-07-28 20:31:17,378 - INFO -   2. F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-07-28 20:31:17,378 - INFO -   3. F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-07-28 20:31:17,378 - INFO - ========== 字幕 #22 处理结束 ==========

