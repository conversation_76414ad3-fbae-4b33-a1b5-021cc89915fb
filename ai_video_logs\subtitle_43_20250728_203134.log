2025-07-28 20:31:34,239 - INFO - ========== 字幕 #43 处理开始 ==========
2025-07-28 20:31:34,239 - INFO - 字幕内容: 同时，为嘉奖女孩的家乡民风淳朴，太子下令免除全村十年赋税，并给予科举入伍的优待。
2025-07-28 20:31:34,239 - INFO - 字幕序号: [3304, 3308]
2025-07-28 20:31:34,240 - INFO - 音频文件详情:
2025-07-28 20:31:34,240 - INFO -   - 路径: output\43.wav
2025-07-28 20:31:34,240 - INFO -   - 时长: 7.11秒
2025-07-28 20:31:34,240 - INFO -   - 验证音频时长: 7.11秒
2025-07-28 20:31:34,240 - INFO - 字幕时间戳信息:
2025-07-28 20:31:34,240 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:34,240 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:34,240 - INFO -   - 根据生成的音频时长(7.11秒)已调整字幕时间戳
2025-07-28 20:31:34,240 - INFO - ========== 开始为字幕 #43 生成 6 套场景方案 ==========
2025-07-28 20:31:34,240 - INFO - 开始查找字幕序号 [3304, 3308] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:34,241 - INFO - 找到related_overlap场景: scene_id=3471, 字幕#3304
2025-07-28 20:31:34,241 - INFO - 找到related_overlap场景: scene_id=3475, 字幕#3308
2025-07-28 20:31:34,242 - INFO - 找到related_between场景: scene_id=3476, 字幕#3308
2025-07-28 20:31:34,242 - INFO - 字幕 #3304 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:34,242 - INFO - 字幕 #3308 找到 1 个overlap场景, 1 个between场景
2025-07-28 20:31:34,242 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 20:31:34,242 - INFO - 开始生成方案 #1
2025-07-28 20:31:34,242 - INFO - 方案 #1: 为字幕#3304选择初始化overlap场景id=3471
2025-07-28 20:31:34,242 - INFO - 方案 #1: 为字幕#3308选择初始化overlap场景id=3475
2025-07-28 20:31:34,242 - INFO - 方案 #1: 初始选择后，当前总时长=4.08秒
2025-07-28 20:31:34,242 - INFO - 方案 #1: 额外between选择后，当前总时长=4.08秒
2025-07-28 20:31:34,242 - INFO - 方案 #1: 额外添加between场景id=3476, 当前总时长=5.12秒
2025-07-28 20:31:34,242 - INFO - 方案 #1: 场景总时长(5.12秒)小于音频时长(7.11秒)，需要延伸填充
2025-07-28 20:31:34,242 - INFO - 方案 #1: 最后一个场景ID: 3476
2025-07-28 20:31:34,242 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 3475
2025-07-28 20:31:34,242 - INFO - 方案 #1: 需要填充时长: 1.99秒
2025-07-28 20:31:34,242 - INFO - 方案 #1: 追加场景 scene_id=3477 (完整时长 1.08秒)
2025-07-28 20:31:34,242 - INFO - 方案 #1: 追加场景 scene_id=3478 (裁剪至 0.91秒)
2025-07-28 20:31:34,242 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 20:31:34,242 - INFO - 方案 #1 调整/填充后最终总时长: 7.11秒
2025-07-28 20:31:34,242 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:34,242 - INFO - 开始生成方案 #2
2025-07-28 20:31:34,242 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,242 - INFO - 开始生成方案 #3
2025-07-28 20:31:34,242 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,242 - INFO - 开始生成方案 #4
2025-07-28 20:31:34,242 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,242 - INFO - 开始生成方案 #5
2025-07-28 20:31:34,242 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,242 - INFO - 开始生成方案 #6
2025-07-28 20:31:34,242 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:34,242 - INFO - ========== 字幕 #43 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:34,242 - INFO - 
----- 处理字幕 #43 的方案 #1 -----
2025-07-28 20:31:34,242 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-28 20:31:34,243 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7yd8i7cu
2025-07-28 20:31:34,243 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3471.mp4 (确认存在: True)
2025-07-28 20:31:34,243 - INFO - 添加场景ID=3471，时长=2.20秒，累计时长=2.20秒
2025-07-28 20:31:34,243 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3475.mp4 (确认存在: True)
2025-07-28 20:31:34,243 - INFO - 添加场景ID=3475，时长=1.88秒，累计时长=4.08秒
2025-07-28 20:31:34,243 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3476.mp4 (确认存在: True)
2025-07-28 20:31:34,243 - INFO - 添加场景ID=3476，时长=1.04秒，累计时长=5.12秒
2025-07-28 20:31:34,243 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3477.mp4 (确认存在: True)
2025-07-28 20:31:34,243 - INFO - 添加场景ID=3477，时长=1.08秒，累计时长=6.20秒
2025-07-28 20:31:34,243 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3478.mp4 (确认存在: True)
2025-07-28 20:31:34,243 - INFO - 添加场景ID=3478，时长=1.56秒，累计时长=7.76秒
2025-07-28 20:31:34,243 - INFO - 准备合并 5 个场景文件，总时长约 7.76秒
2025-07-28 20:31:34,243 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3471.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3475.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3476.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3477.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3478.mp4'

2025-07-28 20:31:34,244 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7yd8i7cu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7yd8i7cu\temp_combined.mp4
2025-07-28 20:31:34,431 - INFO - 合并后的视频时长: 7.88秒，目标音频时长: 7.11秒
2025-07-28 20:31:34,431 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7yd8i7cu\temp_combined.mp4 -ss 0 -to 7.105 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-28 20:31:34,784 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:34,784 - INFO - 目标音频时长: 7.11秒
2025-07-28 20:31:34,784 - INFO - 实际视频时长: 7.14秒
2025-07-28 20:31:34,784 - INFO - 时长差异: 0.04秒 (0.53%)
2025-07-28 20:31:34,784 - INFO - ==========================================
2025-07-28 20:31:34,784 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:34,784 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-28 20:31:34,785 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7yd8i7cu
2025-07-28 20:31:34,828 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:34,828 - INFO -   - 音频时长: 7.11秒
2025-07-28 20:31:34,828 - INFO -   - 视频时长: 7.14秒
2025-07-28 20:31:34,828 - INFO -   - 时长差异: 0.04秒 (0.53%)
2025-07-28 20:31:34,828 - INFO - 
字幕 #43 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:34,828 - INFO - 生成的视频文件:
2025-07-28 20:31:34,828 - INFO -   1. F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-07-28 20:31:34,828 - INFO - ========== 字幕 #43 处理结束 ==========

