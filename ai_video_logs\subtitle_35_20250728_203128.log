2025-07-28 20:31:28,239 - INFO - ========== 字幕 #35 处理开始 ==========
2025-07-28 20:31:28,239 - INFO - 字幕内容: 太子带着浩荡的队伍回来了，金戈铁马，气势非凡。
2025-07-28 20:31:28,239 - INFO - 字幕序号: [3229, 3232]
2025-07-28 20:31:28,240 - INFO - 音频文件详情:
2025-07-28 20:31:28,240 - INFO -   - 路径: output\35.wav
2025-07-28 20:31:28,240 - INFO -   - 时长: 2.45秒
2025-07-28 20:31:28,240 - INFO -   - 验证音频时长: 2.45秒
2025-07-28 20:31:28,240 - INFO - 字幕时间戳信息:
2025-07-28 20:31:28,240 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:28,240 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:28,240 - INFO -   - 根据生成的音频时长(2.45秒)已调整字幕时间戳
2025-07-28 20:31:28,240 - INFO - ========== 开始为字幕 #35 生成 6 套场景方案 ==========
2025-07-28 20:31:28,240 - INFO - 开始查找字幕序号 [3229, 3232] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:28,241 - INFO - 找到related_overlap场景: scene_id=3391, 字幕#3229
2025-07-28 20:31:28,241 - INFO - 找到related_overlap场景: scene_id=3395, 字幕#3232
2025-07-28 20:31:28,241 - INFO - 找到related_between场景: scene_id=3387, 字幕#3229
2025-07-28 20:31:28,241 - INFO - 找到related_between场景: scene_id=3388, 字幕#3229
2025-07-28 20:31:28,241 - INFO - 找到related_between场景: scene_id=3389, 字幕#3229
2025-07-28 20:31:28,241 - INFO - 找到related_between场景: scene_id=3390, 字幕#3229
2025-07-28 20:31:28,242 - INFO - 字幕 #3229 找到 1 个overlap场景, 4 个between场景
2025-07-28 20:31:28,242 - INFO - 字幕 #3232 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:28,242 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-07-28 20:31:28,242 - INFO - 开始生成方案 #1
2025-07-28 20:31:28,242 - INFO - 方案 #1: 为字幕#3229选择初始化overlap场景id=3391
2025-07-28 20:31:28,242 - INFO - 方案 #1: 为字幕#3232选择初始化overlap场景id=3395
2025-07-28 20:31:28,242 - INFO - 方案 #1: 初始选择后，当前总时长=2.12秒
2025-07-28 20:31:28,242 - INFO - 方案 #1: 额外between选择后，当前总时长=2.12秒
2025-07-28 20:31:28,242 - INFO - 方案 #1: 额外添加between场景id=3387, 当前总时长=4.00秒
2025-07-28 20:31:28,242 - INFO - 方案 #1: 场景总时长(4.00秒)大于音频时长(2.45秒)，需要裁剪
2025-07-28 20:31:28,242 - INFO - 调整前总时长: 4.00秒, 目标时长: 2.45秒
2025-07-28 20:31:28,242 - INFO - 需要裁剪 1.55秒
2025-07-28 20:31:28,242 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:28,242 - INFO - 裁剪场景ID=3387：从1.88秒裁剪至1.00秒
2025-07-28 20:31:28,242 - INFO - 裁剪场景ID=3391：从1.12秒裁剪至1.00秒
2025-07-28 20:31:28,242 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.55秒
2025-07-28 20:31:28,242 - INFO - 移除场景ID=3395，时长=1.00秒
2025-07-28 20:31:28,242 - INFO - 调整后总时长: 2.00秒，与目标时长差异: 0.45秒
2025-07-28 20:31:28,242 - INFO - 方案 #1 调整/填充后最终总时长: 2.00秒
2025-07-28 20:31:28,242 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:28,242 - INFO - 开始生成方案 #2
2025-07-28 20:31:28,242 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:28,242 - INFO - 方案 #2: 为字幕#3229选择初始化between场景id=3390
2025-07-28 20:31:28,242 - INFO - 方案 #2: 额外between选择后，当前总时长=1.32秒
2025-07-28 20:31:28,242 - INFO - 方案 #2: 额外添加between场景id=3388, 当前总时长=2.36秒
2025-07-28 20:31:28,242 - INFO - 方案 #2: 额外添加between场景id=3389, 当前总时长=3.44秒
2025-07-28 20:31:28,242 - INFO - 方案 #2: 场景总时长(3.44秒)大于音频时长(2.45秒)，需要裁剪
2025-07-28 20:31:28,242 - INFO - 调整前总时长: 3.44秒, 目标时长: 2.45秒
2025-07-28 20:31:28,242 - INFO - 需要裁剪 0.98秒
2025-07-28 20:31:28,242 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:28,242 - INFO - 裁剪场景ID=3390：从1.32秒裁剪至1.00秒
2025-07-28 20:31:28,242 - INFO - 裁剪场景ID=3389：从1.08秒裁剪至1.00秒
2025-07-28 20:31:28,242 - INFO - 裁剪场景ID=3388：从1.04秒裁剪至1.00秒
2025-07-28 20:31:28,242 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.55秒
2025-07-28 20:31:28,242 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.55秒
2025-07-28 20:31:28,242 - INFO - 方案 #2 调整/填充后最终总时长: 3.00秒
2025-07-28 20:31:28,242 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:28,242 - INFO - 开始生成方案 #3
2025-07-28 20:31:28,242 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:28,242 - INFO - 开始生成方案 #4
2025-07-28 20:31:28,242 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:28,242 - INFO - 开始生成方案 #5
2025-07-28 20:31:28,242 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:28,242 - INFO - 开始生成方案 #6
2025-07-28 20:31:28,242 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:28,242 - INFO - ========== 字幕 #35 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:28,242 - INFO - 
----- 处理字幕 #35 的方案 #1 -----
2025-07-28 20:31:28,242 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-28 20:31:28,243 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxchvox3z
2025-07-28 20:31:28,244 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3391.mp4 (确认存在: True)
2025-07-28 20:31:28,244 - INFO - 添加场景ID=3391，时长=1.12秒，累计时长=1.12秒
2025-07-28 20:31:28,244 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3387.mp4 (确认存在: True)
2025-07-28 20:31:28,244 - INFO - 添加场景ID=3387，时长=1.88秒，累计时长=3.00秒
2025-07-28 20:31:28,244 - INFO - 准备合并 2 个场景文件，总时长约 3.00秒
2025-07-28 20:31:28,244 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3391.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3387.mp4'

2025-07-28 20:31:28,244 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxchvox3z\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxchvox3z\temp_combined.mp4
2025-07-28 20:31:28,385 - INFO - 合并后的视频时长: 3.05秒，目标音频时长: 2.45秒
2025-07-28 20:31:28,385 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxchvox3z\temp_combined.mp4 -ss 0 -to 2.453 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-28 20:31:28,609 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:28,609 - INFO - 目标音频时长: 2.45秒
2025-07-28 20:31:28,609 - INFO - 实际视频时长: 2.50秒
2025-07-28 20:31:28,609 - INFO - 时长差异: 0.05秒 (2.04%)
2025-07-28 20:31:28,609 - INFO - ==========================================
2025-07-28 20:31:28,609 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:28,609 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-28 20:31:28,610 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxchvox3z
2025-07-28 20:31:28,651 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:28,651 - INFO -   - 音频时长: 2.45秒
2025-07-28 20:31:28,651 - INFO -   - 视频时长: 2.50秒
2025-07-28 20:31:28,652 - INFO -   - 时长差异: 0.05秒 (2.04%)
2025-07-28 20:31:28,652 - INFO - 
----- 处理字幕 #35 的方案 #2 -----
2025-07-28 20:31:28,652 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-28 20:31:28,652 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxfv5lgbs
2025-07-28 20:31:28,652 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3390.mp4 (确认存在: True)
2025-07-28 20:31:28,652 - INFO - 添加场景ID=3390，时长=1.32秒，累计时长=1.32秒
2025-07-28 20:31:28,652 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3388.mp4 (确认存在: True)
2025-07-28 20:31:28,652 - INFO - 添加场景ID=3388，时长=1.04秒，累计时长=2.36秒
2025-07-28 20:31:28,652 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3389.mp4 (确认存在: True)
2025-07-28 20:31:28,652 - INFO - 添加场景ID=3389，时长=1.08秒，累计时长=3.44秒
2025-07-28 20:31:28,652 - INFO - 准备合并 3 个场景文件，总时长约 3.44秒
2025-07-28 20:31:28,652 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3390.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3388.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3389.mp4'

2025-07-28 20:31:28,652 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxfv5lgbs\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxfv5lgbs\temp_combined.mp4
2025-07-28 20:31:28,812 - INFO - 合并后的视频时长: 3.51秒，目标音频时长: 2.45秒
2025-07-28 20:31:28,812 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxfv5lgbs\temp_combined.mp4 -ss 0 -to 2.453 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-28 20:31:29,084 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:29,084 - INFO - 目标音频时长: 2.45秒
2025-07-28 20:31:29,084 - INFO - 实际视频时长: 2.50秒
2025-07-28 20:31:29,084 - INFO - 时长差异: 0.05秒 (2.04%)
2025-07-28 20:31:29,084 - INFO - ==========================================
2025-07-28 20:31:29,084 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:29,084 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-28 20:31:29,085 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxfv5lgbs
2025-07-28 20:31:29,127 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:29,127 - INFO -   - 音频时长: 2.45秒
2025-07-28 20:31:29,127 - INFO -   - 视频时长: 2.50秒
2025-07-28 20:31:29,127 - INFO -   - 时长差异: 0.05秒 (2.04%)
2025-07-28 20:31:29,127 - INFO - 
字幕 #35 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:29,127 - INFO - 生成的视频文件:
2025-07-28 20:31:29,127 - INFO -   1. F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-07-28 20:31:29,127 - INFO -   2. F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-07-28 20:31:29,127 - INFO - ========== 字幕 #35 处理结束 ==========

