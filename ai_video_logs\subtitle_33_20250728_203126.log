2025-07-28 20:31:26,906 - INFO - ========== 字幕 #33 处理开始 ==========
2025-07-28 20:31:26,906 - INFO - 字幕内容: 太子对女孩的救命之恩感激不尽，并记下了她的名字。
2025-07-28 20:31:26,906 - INFO - 字幕序号: [2272, 2273]
2025-07-28 20:31:26,906 - INFO - 音频文件详情:
2025-07-28 20:31:26,906 - INFO -   - 路径: output\33.wav
2025-07-28 20:31:26,906 - INFO -   - 时长: 2.65秒
2025-07-28 20:31:26,907 - INFO -   - 验证音频时长: 2.65秒
2025-07-28 20:31:26,907 - INFO - 字幕时间戳信息:
2025-07-28 20:31:26,907 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:26,907 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:26,907 - INFO -   - 根据生成的音频时长(2.65秒)已调整字幕时间戳
2025-07-28 20:31:26,907 - INFO - ========== 开始为字幕 #33 生成 6 套场景方案 ==========
2025-07-28 20:31:26,907 - INFO - 开始查找字幕序号 [2272, 2273] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:26,907 - INFO - 找到related_overlap场景: scene_id=2371, 字幕#2272
2025-07-28 20:31:26,908 - INFO - 字幕 #2272 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:26,908 - INFO - 字幕 #2273 找到 0 个overlap场景, 0 个between场景
2025-07-28 20:31:26,908 - WARNING - 字幕 #2273 没有找到任何匹配场景!
2025-07-28 20:31:26,908 - INFO - 共收集 1 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:26,908 - INFO - 开始生成方案 #1
2025-07-28 20:31:26,908 - INFO - 方案 #1: 为字幕#2272选择初始化overlap场景id=2371
2025-07-28 20:31:26,908 - INFO - 方案 #1: 初始选择后，当前总时长=2.60秒
2025-07-28 20:31:26,908 - INFO - 方案 #1: 额外between选择后，当前总时长=2.60秒
2025-07-28 20:31:26,908 - INFO - 方案 #1: 场景总时长(2.60秒)小于音频时长(2.65秒)，需要延伸填充
2025-07-28 20:31:26,908 - INFO - 方案 #1: 最后一个场景ID: 2371
2025-07-28 20:31:26,908 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2370
2025-07-28 20:31:26,908 - INFO - 方案 #1: 需要填充时长: 0.05秒
2025-07-28 20:31:26,908 - INFO - 方案 #1: 追加场景 scene_id=2372 (裁剪至 0.05秒)
2025-07-28 20:31:26,909 - INFO - 方案 #1: 成功填充至目标时长
2025-07-28 20:31:26,909 - INFO - 方案 #1 调整/填充后最终总时长: 2.65秒
2025-07-28 20:31:26,909 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:26,909 - INFO - 开始生成方案 #2
2025-07-28 20:31:26,909 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,909 - INFO - 开始生成方案 #3
2025-07-28 20:31:26,909 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,909 - INFO - 开始生成方案 #4
2025-07-28 20:31:26,909 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,909 - INFO - 开始生成方案 #5
2025-07-28 20:31:26,909 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,909 - INFO - 开始生成方案 #6
2025-07-28 20:31:26,909 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:26,909 - INFO - ========== 字幕 #33 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:26,909 - INFO - 
----- 处理字幕 #33 的方案 #1 -----
2025-07-28 20:31:26,909 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-28 20:31:26,909 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsy0ygczq
2025-07-28 20:31:26,909 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2371.mp4 (确认存在: True)
2025-07-28 20:31:26,910 - INFO - 添加场景ID=2371，时长=2.60秒，累计时长=2.60秒
2025-07-28 20:31:26,910 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2372.mp4 (确认存在: True)
2025-07-28 20:31:26,910 - INFO - 添加场景ID=2372，时长=1.76秒，累计时长=4.36秒
2025-07-28 20:31:26,910 - INFO - 场景总时长(4.36秒)已达到音频时长(2.65秒)的1.5倍，停止添加场景
2025-07-28 20:31:26,910 - INFO - 准备合并 2 个场景文件，总时长约 4.36秒
2025-07-28 20:31:26,910 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2371.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2372.mp4'

2025-07-28 20:31:26,910 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsy0ygczq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsy0ygczq\temp_combined.mp4
2025-07-28 20:31:27,042 - INFO - 合并后的视频时长: 4.41秒，目标音频时长: 2.65秒
2025-07-28 20:31:27,042 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsy0ygczq\temp_combined.mp4 -ss 0 -to 2.651 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-28 20:31:27,289 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:27,289 - INFO - 目标音频时长: 2.65秒
2025-07-28 20:31:27,289 - INFO - 实际视频时长: 2.70秒
2025-07-28 20:31:27,289 - INFO - 时长差异: 0.05秒 (1.96%)
2025-07-28 20:31:27,289 - INFO - ==========================================
2025-07-28 20:31:27,289 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:27,289 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-28 20:31:27,289 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsy0ygczq
2025-07-28 20:31:27,330 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:27,330 - INFO -   - 音频时长: 2.65秒
2025-07-28 20:31:27,330 - INFO -   - 视频时长: 2.70秒
2025-07-28 20:31:27,330 - INFO -   - 时长差异: 0.05秒 (1.96%)
2025-07-28 20:31:27,330 - INFO - 
字幕 #33 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:27,330 - INFO - 生成的视频文件:
2025-07-28 20:31:27,330 - INFO -   1. F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-07-28 20:31:27,330 - INFO - ========== 字幕 #33 处理结束 ==========

