2025-07-28 20:31:27,330 - INFO - ========== 字幕 #34 处理开始 ==========
2025-07-28 20:31:27,331 - INFO - 字幕内容: 几天后，当村民们还在议论女孩的义举时，村口传来了轰动。
2025-07-28 20:31:27,331 - INFO - 字幕序号: [2350, 2352]
2025-07-28 20:31:27,331 - INFO - 音频文件详情:
2025-07-28 20:31:27,331 - INFO -   - 路径: output\34.wav
2025-07-28 20:31:27,331 - INFO -   - 时长: 3.29秒
2025-07-28 20:31:27,331 - INFO -   - 验证音频时长: 3.29秒
2025-07-28 20:31:27,331 - INFO - 字幕时间戳信息:
2025-07-28 20:31:27,331 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:27,331 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:27,331 - INFO -   - 根据生成的音频时长(3.29秒)已调整字幕时间戳
2025-07-28 20:31:27,331 - INFO - ========== 开始为字幕 #34 生成 6 套场景方案 ==========
2025-07-28 20:31:27,331 - INFO - 开始查找字幕序号 [2350, 2352] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:27,332 - INFO - 找到related_overlap场景: scene_id=2436, 字幕#2350
2025-07-28 20:31:27,332 - INFO - 找到related_overlap场景: scene_id=2437, 字幕#2352
2025-07-28 20:31:27,332 - INFO - 找到related_overlap场景: scene_id=2438, 字幕#2352
2025-07-28 20:31:27,332 - INFO - 字幕 #2350 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:27,332 - INFO - 字幕 #2352 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:27,332 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:27,332 - INFO - 开始生成方案 #1
2025-07-28 20:31:27,332 - INFO - 方案 #1: 为字幕#2350选择初始化overlap场景id=2436
2025-07-28 20:31:27,332 - INFO - 方案 #1: 为字幕#2352选择初始化overlap场景id=2438
2025-07-28 20:31:27,332 - INFO - 方案 #1: 初始选择后，当前总时长=3.48秒
2025-07-28 20:31:27,332 - INFO - 方案 #1: 额外between选择后，当前总时长=3.48秒
2025-07-28 20:31:27,332 - INFO - 方案 #1: 场景总时长(3.48秒)大于音频时长(3.29秒)，需要裁剪
2025-07-28 20:31:27,332 - INFO - 调整前总时长: 3.48秒, 目标时长: 3.29秒
2025-07-28 20:31:27,332 - INFO - 需要裁剪 0.20秒
2025-07-28 20:31:27,332 - INFO - 裁剪最长场景ID=2438：从1.80秒裁剪至1.60秒
2025-07-28 20:31:27,332 - INFO - 调整后总时长: 3.29秒，与目标时长差异: 0.00秒
2025-07-28 20:31:27,332 - INFO - 方案 #1 调整/填充后最终总时长: 3.29秒
2025-07-28 20:31:27,332 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:27,332 - INFO - 开始生成方案 #2
2025-07-28 20:31:27,334 - INFO - 方案 #2: 为字幕#2352选择初始化overlap场景id=2437
2025-07-28 20:31:27,334 - INFO - 方案 #2: 初始选择后，当前总时长=1.60秒
2025-07-28 20:31:27,334 - INFO - 方案 #2: 额外between选择后，当前总时长=1.60秒
2025-07-28 20:31:27,334 - INFO - 方案 #2: 场景总时长(1.60秒)小于音频时长(3.29秒)，需要延伸填充
2025-07-28 20:31:27,334 - INFO - 方案 #2: 最后一个场景ID: 2437
2025-07-28 20:31:27,334 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2436
2025-07-28 20:31:27,334 - INFO - 方案 #2: 需要填充时长: 1.69秒
2025-07-28 20:31:27,334 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2438
2025-07-28 20:31:27,334 - INFO - 方案 #2: 追加场景 scene_id=2439 (裁剪至 1.69秒)
2025-07-28 20:31:27,334 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:27,334 - INFO - 方案 #2 调整/填充后最终总时长: 3.29秒
2025-07-28 20:31:27,334 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:27,334 - INFO - 开始生成方案 #3
2025-07-28 20:31:27,334 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:27,334 - INFO - 开始生成方案 #4
2025-07-28 20:31:27,334 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:27,334 - INFO - 开始生成方案 #5
2025-07-28 20:31:27,334 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:27,334 - INFO - 开始生成方案 #6
2025-07-28 20:31:27,334 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:27,334 - INFO - ========== 字幕 #34 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:27,334 - INFO - 
----- 处理字幕 #34 的方案 #1 -----
2025-07-28 20:31:27,334 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-28 20:31:27,334 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplu6aoed9
2025-07-28 20:31:27,335 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2436.mp4 (确认存在: True)
2025-07-28 20:31:27,335 - INFO - 添加场景ID=2436，时长=1.68秒，累计时长=1.68秒
2025-07-28 20:31:27,335 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2438.mp4 (确认存在: True)
2025-07-28 20:31:27,335 - INFO - 添加场景ID=2438，时长=1.80秒，累计时长=3.48秒
2025-07-28 20:31:27,335 - INFO - 准备合并 2 个场景文件，总时长约 3.48秒
2025-07-28 20:31:27,335 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2436.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2438.mp4'

2025-07-28 20:31:27,335 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplu6aoed9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplu6aoed9\temp_combined.mp4
2025-07-28 20:31:27,494 - INFO - 合并后的视频时长: 3.53秒，目标音频时长: 3.29秒
2025-07-28 20:31:27,494 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplu6aoed9\temp_combined.mp4 -ss 0 -to 3.285 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-28 20:31:27,747 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:27,747 - INFO - 目标音频时长: 3.29秒
2025-07-28 20:31:27,747 - INFO - 实际视频时长: 3.34秒
2025-07-28 20:31:27,747 - INFO - 时长差异: 0.06秒 (1.77%)
2025-07-28 20:31:27,747 - INFO - ==========================================
2025-07-28 20:31:27,747 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:27,747 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-28 20:31:27,748 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplu6aoed9
2025-07-28 20:31:27,796 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:27,796 - INFO -   - 音频时长: 3.29秒
2025-07-28 20:31:27,796 - INFO -   - 视频时长: 3.34秒
2025-07-28 20:31:27,796 - INFO -   - 时长差异: 0.06秒 (1.77%)
2025-07-28 20:31:27,796 - INFO - 
----- 处理字幕 #34 的方案 #2 -----
2025-07-28 20:31:27,796 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-28 20:31:27,796 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi7acqa0v
2025-07-28 20:31:27,797 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2437.mp4 (确认存在: True)
2025-07-28 20:31:27,797 - INFO - 添加场景ID=2437，时长=1.60秒，累计时长=1.60秒
2025-07-28 20:31:27,797 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2439.mp4 (确认存在: True)
2025-07-28 20:31:27,797 - INFO - 添加场景ID=2439，时长=1.88秒，累计时长=3.48秒
2025-07-28 20:31:27,797 - INFO - 准备合并 2 个场景文件，总时长约 3.48秒
2025-07-28 20:31:27,797 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2437.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2439.mp4'

2025-07-28 20:31:27,797 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpi7acqa0v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpi7acqa0v\temp_combined.mp4
2025-07-28 20:31:27,922 - INFO - 合并后的视频时长: 3.53秒，目标音频时长: 3.29秒
2025-07-28 20:31:27,922 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpi7acqa0v\temp_combined.mp4 -ss 0 -to 3.285 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-28 20:31:28,195 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:28,195 - INFO - 目标音频时长: 3.29秒
2025-07-28 20:31:28,195 - INFO - 实际视频时长: 3.34秒
2025-07-28 20:31:28,195 - INFO - 时长差异: 0.06秒 (1.77%)
2025-07-28 20:31:28,195 - INFO - ==========================================
2025-07-28 20:31:28,195 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:28,195 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-28 20:31:28,196 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi7acqa0v
2025-07-28 20:31:28,238 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:28,238 - INFO -   - 音频时长: 3.29秒
2025-07-28 20:31:28,238 - INFO -   - 视频时长: 3.34秒
2025-07-28 20:31:28,238 - INFO -   - 时长差异: 0.06秒 (1.77%)
2025-07-28 20:31:28,239 - INFO - 
字幕 #34 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:28,239 - INFO - 生成的视频文件:
2025-07-28 20:31:28,239 - INFO -   1. F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-07-28 20:31:28,239 - INFO -   2. F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-07-28 20:31:28,239 - INFO - ========== 字幕 #34 处理结束 ==========

