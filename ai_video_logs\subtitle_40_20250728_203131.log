2025-07-28 20:31:31,490 - INFO - ========== 字幕 #40 处理开始 ==========
2025-07-28 20:31:31,491 - INFO - 字幕内容: 女孩却心系家人与乡亲，婉拒了太子的好意。
2025-07-28 20:31:31,491 - INFO - 字幕序号: [3278, 3281]
2025-07-28 20:31:31,491 - INFO - 音频文件详情:
2025-07-28 20:31:31,491 - INFO -   - 路径: output\40.wav
2025-07-28 20:31:31,491 - INFO -   - 时长: 2.90秒
2025-07-28 20:31:31,491 - INFO -   - 验证音频时长: 2.90秒
2025-07-28 20:31:31,491 - INFO - 字幕时间戳信息:
2025-07-28 20:31:31,491 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:31,491 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:31,491 - INFO -   - 根据生成的音频时长(2.90秒)已调整字幕时间戳
2025-07-28 20:31:31,491 - INFO - ========== 开始为字幕 #40 生成 6 套场景方案 ==========
2025-07-28 20:31:31,491 - INFO - 开始查找字幕序号 [3278, 3281] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:31,492 - INFO - 找到related_overlap场景: scene_id=3437, 字幕#3278
2025-07-28 20:31:31,492 - INFO - 找到related_overlap场景: scene_id=3438, 字幕#3278
2025-07-28 20:31:31,492 - INFO - 找到related_overlap场景: scene_id=3442, 字幕#3281
2025-07-28 20:31:31,492 - INFO - 找到related_overlap场景: scene_id=3443, 字幕#3281
2025-07-28 20:31:31,492 - INFO - 找到related_between场景: scene_id=3444, 字幕#3281
2025-07-28 20:31:31,492 - INFO - 字幕 #3278 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:31,492 - INFO - 字幕 #3281 找到 2 个overlap场景, 1 个between场景
2025-07-28 20:31:31,492 - INFO - 共收集 4 个未使用的overlap场景和 1 个未使用的between场景
2025-07-28 20:31:31,492 - INFO - 开始生成方案 #1
2025-07-28 20:31:31,492 - INFO - 方案 #1: 为字幕#3278选择初始化overlap场景id=3437
2025-07-28 20:31:31,492 - INFO - 方案 #1: 为字幕#3281选择初始化overlap场景id=3443
2025-07-28 20:31:31,492 - INFO - 方案 #1: 初始选择后，当前总时长=3.20秒
2025-07-28 20:31:31,492 - INFO - 方案 #1: 额外between选择后，当前总时长=3.20秒
2025-07-28 20:31:31,492 - INFO - 方案 #1: 场景总时长(3.20秒)大于音频时长(2.90秒)，需要裁剪
2025-07-28 20:31:31,492 - INFO - 调整前总时长: 3.20秒, 目标时长: 2.90秒
2025-07-28 20:31:31,492 - INFO - 需要裁剪 0.30秒
2025-07-28 20:31:31,493 - INFO - 裁剪最长场景ID=3437：从1.88秒裁剪至1.58秒
2025-07-28 20:31:31,493 - INFO - 调整后总时长: 2.90秒，与目标时长差异: 0.00秒
2025-07-28 20:31:31,493 - INFO - 方案 #1 调整/填充后最终总时长: 2.90秒
2025-07-28 20:31:31,493 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:31,493 - INFO - 开始生成方案 #2
2025-07-28 20:31:31,493 - INFO - 方案 #2: 为字幕#3278选择初始化overlap场景id=3438
2025-07-28 20:31:31,493 - INFO - 方案 #2: 为字幕#3281选择初始化overlap场景id=3442
2025-07-28 20:31:31,493 - INFO - 方案 #2: 初始选择后，当前总时长=3.40秒
2025-07-28 20:31:31,493 - INFO - 方案 #2: 额外between选择后，当前总时长=3.40秒
2025-07-28 20:31:31,493 - INFO - 方案 #2: 场景总时长(3.40秒)大于音频时长(2.90秒)，需要裁剪
2025-07-28 20:31:31,493 - INFO - 调整前总时长: 3.40秒, 目标时长: 2.90秒
2025-07-28 20:31:31,493 - INFO - 需要裁剪 0.50秒
2025-07-28 20:31:31,493 - INFO - 裁剪最长场景ID=3438：从2.28秒裁剪至1.78秒
2025-07-28 20:31:31,493 - INFO - 调整后总时长: 2.90秒，与目标时长差异: 0.00秒
2025-07-28 20:31:31,493 - INFO - 方案 #2 调整/填充后最终总时长: 2.90秒
2025-07-28 20:31:31,493 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:31,493 - INFO - 开始生成方案 #3
2025-07-28 20:31:31,493 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:31,493 - INFO - 方案 #3: 为字幕#3281选择初始化between场景id=3444
2025-07-28 20:31:31,493 - INFO - 方案 #3: 额外between选择后，当前总时长=1.24秒
2025-07-28 20:31:31,493 - INFO - 方案 #3: 场景总时长(1.24秒)小于音频时长(2.90秒)，需要延伸填充
2025-07-28 20:31:31,493 - INFO - 方案 #3: 最后一个场景ID: 3444
2025-07-28 20:31:31,493 - INFO - 方案 #3: 找到最后一个场景在原始列表中的索引: 3443
2025-07-28 20:31:31,493 - INFO - 方案 #3: 需要填充时长: 1.66秒
2025-07-28 20:31:31,493 - INFO - 方案 #3: 追加场景 scene_id=3445 (裁剪至 1.66秒)
2025-07-28 20:31:31,493 - INFO - 方案 #3: 成功填充至目标时长
2025-07-28 20:31:31,493 - INFO - 方案 #3 调整/填充后最终总时长: 2.90秒
2025-07-28 20:31:31,493 - INFO - 方案 #3 添加到方案列表
2025-07-28 20:31:31,493 - INFO - 开始生成方案 #4
2025-07-28 20:31:31,493 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:31,493 - INFO - 开始生成方案 #5
2025-07-28 20:31:31,493 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:31,493 - INFO - 开始生成方案 #6
2025-07-28 20:31:31,493 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:31,493 - INFO - ========== 字幕 #40 的 3 套有效场景方案生成完成 ==========
2025-07-28 20:31:31,493 - INFO - 
----- 处理字幕 #40 的方案 #1 -----
2025-07-28 20:31:31,493 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-28 20:31:31,494 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbx8s5vhs
2025-07-28 20:31:31,494 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3437.mp4 (确认存在: True)
2025-07-28 20:31:31,494 - INFO - 添加场景ID=3437，时长=1.88秒，累计时长=1.88秒
2025-07-28 20:31:31,494 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3443.mp4 (确认存在: True)
2025-07-28 20:31:31,494 - INFO - 添加场景ID=3443，时长=1.32秒，累计时长=3.20秒
2025-07-28 20:31:31,494 - INFO - 准备合并 2 个场景文件，总时长约 3.20秒
2025-07-28 20:31:31,494 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3437.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3443.mp4'

2025-07-28 20:31:31,495 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbx8s5vhs\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbx8s5vhs\temp_combined.mp4
2025-07-28 20:31:31,640 - INFO - 合并后的视频时长: 3.25秒，目标音频时长: 2.90秒
2025-07-28 20:31:31,640 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbx8s5vhs\temp_combined.mp4 -ss 0 -to 2.896 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-28 20:31:31,884 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:31,884 - INFO - 目标音频时长: 2.90秒
2025-07-28 20:31:31,884 - INFO - 实际视频时长: 2.94秒
2025-07-28 20:31:31,884 - INFO - 时长差异: 0.05秒 (1.62%)
2025-07-28 20:31:31,884 - INFO - ==========================================
2025-07-28 20:31:31,884 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:31,884 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-28 20:31:31,884 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbx8s5vhs
2025-07-28 20:31:31,928 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:31,928 - INFO -   - 音频时长: 2.90秒
2025-07-28 20:31:31,928 - INFO -   - 视频时长: 2.94秒
2025-07-28 20:31:31,928 - INFO -   - 时长差异: 0.05秒 (1.62%)
2025-07-28 20:31:31,928 - INFO - 
----- 处理字幕 #40 的方案 #2 -----
2025-07-28 20:31:31,928 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-28 20:31:31,928 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8mlpz5r4
2025-07-28 20:31:31,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3438.mp4 (确认存在: True)
2025-07-28 20:31:31,929 - INFO - 添加场景ID=3438，时长=2.28秒，累计时长=2.28秒
2025-07-28 20:31:31,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3442.mp4 (确认存在: True)
2025-07-28 20:31:31,929 - INFO - 添加场景ID=3442，时长=1.12秒，累计时长=3.40秒
2025-07-28 20:31:31,929 - INFO - 准备合并 2 个场景文件，总时长约 3.40秒
2025-07-28 20:31:31,930 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3438.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3442.mp4'

2025-07-28 20:31:31,930 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8mlpz5r4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8mlpz5r4\temp_combined.mp4
2025-07-28 20:31:32,069 - INFO - 合并后的视频时长: 3.45秒，目标音频时长: 2.90秒
2025-07-28 20:31:32,069 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8mlpz5r4\temp_combined.mp4 -ss 0 -to 2.896 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-28 20:31:32,322 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:32,322 - INFO - 目标音频时长: 2.90秒
2025-07-28 20:31:32,322 - INFO - 实际视频时长: 2.94秒
2025-07-28 20:31:32,322 - INFO - 时长差异: 0.05秒 (1.62%)
2025-07-28 20:31:32,322 - INFO - ==========================================
2025-07-28 20:31:32,322 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:32,322 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-28 20:31:32,322 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8mlpz5r4
2025-07-28 20:31:32,369 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:32,369 - INFO -   - 音频时长: 2.90秒
2025-07-28 20:31:32,369 - INFO -   - 视频时长: 2.94秒
2025-07-28 20:31:32,369 - INFO -   - 时长差异: 0.05秒 (1.62%)
2025-07-28 20:31:32,369 - INFO - 
----- 处理字幕 #40 的方案 #3 -----
2025-07-28 20:31:32,369 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-28 20:31:32,370 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6l8xru3x
2025-07-28 20:31:32,370 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3444.mp4 (确认存在: True)
2025-07-28 20:31:32,370 - INFO - 添加场景ID=3444，时长=1.24秒，累计时长=1.24秒
2025-07-28 20:31:32,370 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3445.mp4 (确认存在: True)
2025-07-28 20:31:32,370 - INFO - 添加场景ID=3445，时长=2.20秒，累计时长=3.44秒
2025-07-28 20:31:32,371 - INFO - 准备合并 2 个场景文件，总时长约 3.44秒
2025-07-28 20:31:32,371 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3444.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3445.mp4'

2025-07-28 20:31:32,371 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6l8xru3x\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6l8xru3x\temp_combined.mp4
2025-07-28 20:31:32,506 - INFO - 合并后的视频时长: 3.49秒，目标音频时长: 2.90秒
2025-07-28 20:31:32,506 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6l8xru3x\temp_combined.mp4 -ss 0 -to 2.896 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-28 20:31:32,747 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:32,748 - INFO - 目标音频时长: 2.90秒
2025-07-28 20:31:32,748 - INFO - 实际视频时长: 2.94秒
2025-07-28 20:31:32,748 - INFO - 时长差异: 0.05秒 (1.62%)
2025-07-28 20:31:32,748 - INFO - ==========================================
2025-07-28 20:31:32,748 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:32,748 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-28 20:31:32,748 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6l8xru3x
2025-07-28 20:31:32,791 - INFO - 方案 #3 处理完成:
2025-07-28 20:31:32,791 - INFO -   - 音频时长: 2.90秒
2025-07-28 20:31:32,791 - INFO -   - 视频时长: 2.94秒
2025-07-28 20:31:32,791 - INFO -   - 时长差异: 0.05秒 (1.62%)
2025-07-28 20:31:32,791 - INFO - 
字幕 #40 处理完成，成功生成 3/3 套方案
2025-07-28 20:31:32,791 - INFO - 生成的视频文件:
2025-07-28 20:31:32,791 - INFO -   1. F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-07-28 20:31:32,791 - INFO -   2. F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-07-28 20:31:32,791 - INFO -   3. F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-07-28 20:31:32,791 - INFO - ========== 字幕 #40 处理结束 ==========

