2025-07-28 20:31:23,301 - INFO - ========== 字幕 #29 处理开始 ==========
2025-07-28 20:31:23,301 - INFO - 字幕内容: 不久后，女孩在山洞中发现一个被巨石困住的男人。
2025-07-28 20:31:23,301 - INFO - 字幕序号: [2170, 2181]
2025-07-28 20:31:23,302 - INFO - 音频文件详情:
2025-07-28 20:31:23,302 - INFO -   - 路径: output\29.wav
2025-07-28 20:31:23,302 - INFO -   - 时长: 2.29秒
2025-07-28 20:31:23,302 - INFO -   - 验证音频时长: 2.29秒
2025-07-28 20:31:23,302 - INFO - 字幕时间戳信息:
2025-07-28 20:31:23,302 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:23,302 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:23,302 - INFO -   - 根据生成的音频时长(2.29秒)已调整字幕时间戳
2025-07-28 20:31:23,302 - INFO - ========== 开始为字幕 #29 生成 6 套场景方案 ==========
2025-07-28 20:31:23,302 - INFO - 开始查找字幕序号 [2170, 2181] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:23,303 - INFO - 找到related_overlap场景: scene_id=2268, 字幕#2170
2025-07-28 20:31:23,303 - INFO - 找到related_overlap场景: scene_id=2276, 字幕#2181
2025-07-28 20:31:23,303 - INFO - 找到related_overlap场景: scene_id=2277, 字幕#2181
2025-07-28 20:31:23,304 - INFO - 字幕 #2170 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:23,304 - INFO - 字幕 #2181 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:23,304 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:23,304 - INFO - 开始生成方案 #1
2025-07-28 20:31:23,304 - INFO - 方案 #1: 为字幕#2170选择初始化overlap场景id=2268
2025-07-28 20:31:23,304 - INFO - 方案 #1: 为字幕#2181选择初始化overlap场景id=2277
2025-07-28 20:31:23,304 - INFO - 方案 #1: 初始选择后，当前总时长=3.36秒
2025-07-28 20:31:23,304 - INFO - 方案 #1: 额外between选择后，当前总时长=3.36秒
2025-07-28 20:31:23,304 - INFO - 方案 #1: 场景总时长(3.36秒)大于音频时长(2.29秒)，需要裁剪
2025-07-28 20:31:23,304 - INFO - 调整前总时长: 3.36秒, 目标时长: 2.29秒
2025-07-28 20:31:23,304 - INFO - 需要裁剪 1.07秒
2025-07-28 20:31:23,304 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:23,304 - INFO - 裁剪场景ID=2277：从1.80秒裁剪至1.00秒
2025-07-28 20:31:23,304 - INFO - 裁剪场景ID=2268：从1.56秒裁剪至1.29秒
2025-07-28 20:31:23,304 - INFO - 调整后总时长: 2.29秒，与目标时长差异: 0.00秒
2025-07-28 20:31:23,304 - INFO - 方案 #1 调整/填充后最终总时长: 2.29秒
2025-07-28 20:31:23,304 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:23,304 - INFO - 开始生成方案 #2
2025-07-28 20:31:23,304 - INFO - 方案 #2: 为字幕#2181选择初始化overlap场景id=2276
2025-07-28 20:31:23,305 - INFO - 方案 #2: 初始选择后，当前总时长=1.76秒
2025-07-28 20:31:23,305 - INFO - 方案 #2: 额外between选择后，当前总时长=1.76秒
2025-07-28 20:31:23,305 - INFO - 方案 #2: 场景总时长(1.76秒)小于音频时长(2.29秒)，需要延伸填充
2025-07-28 20:31:23,305 - INFO - 方案 #2: 最后一个场景ID: 2276
2025-07-28 20:31:23,305 - INFO - 方案 #2: 找到最后一个场景在原始列表中的索引: 2275
2025-07-28 20:31:23,305 - INFO - 方案 #2: 需要填充时长: 0.53秒
2025-07-28 20:31:23,305 - INFO - 方案 #2: 跳过已使用的场景: scene_id=2277
2025-07-28 20:31:23,305 - INFO - 方案 #2: 追加场景 scene_id=2278 (裁剪至 0.53秒)
2025-07-28 20:31:23,305 - INFO - 方案 #2: 成功填充至目标时长
2025-07-28 20:31:23,305 - INFO - 方案 #2 调整/填充后最终总时长: 2.29秒
2025-07-28 20:31:23,305 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:23,305 - INFO - 开始生成方案 #3
2025-07-28 20:31:23,305 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:23,305 - INFO - 开始生成方案 #4
2025-07-28 20:31:23,305 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:23,305 - INFO - 开始生成方案 #5
2025-07-28 20:31:23,305 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:23,305 - INFO - 开始生成方案 #6
2025-07-28 20:31:23,305 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:23,305 - INFO - ========== 字幕 #29 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:23,305 - INFO - 
----- 处理字幕 #29 的方案 #1 -----
2025-07-28 20:31:23,305 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-28 20:31:23,305 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpa7o0ci47
2025-07-28 20:31:23,306 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2268.mp4 (确认存在: True)
2025-07-28 20:31:23,306 - INFO - 添加场景ID=2268，时长=1.56秒，累计时长=1.56秒
2025-07-28 20:31:23,306 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2277.mp4 (确认存在: True)
2025-07-28 20:31:23,306 - INFO - 添加场景ID=2277，时长=1.80秒，累计时长=3.36秒
2025-07-28 20:31:23,306 - INFO - 准备合并 2 个场景文件，总时长约 3.36秒
2025-07-28 20:31:23,306 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2268.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2277.mp4'

2025-07-28 20:31:23,306 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpa7o0ci47\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpa7o0ci47\temp_combined.mp4
2025-07-28 20:31:23,446 - INFO - 合并后的视频时长: 3.41秒，目标音频时长: 2.29秒
2025-07-28 20:31:23,447 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpa7o0ci47\temp_combined.mp4 -ss 0 -to 2.286 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-28 20:31:23,674 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:23,675 - INFO - 目标音频时长: 2.29秒
2025-07-28 20:31:23,675 - INFO - 实际视频时长: 2.34秒
2025-07-28 20:31:23,675 - INFO - 时长差异: 0.06秒 (2.49%)
2025-07-28 20:31:23,675 - INFO - ==========================================
2025-07-28 20:31:23,675 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:23,675 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-28 20:31:23,675 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpa7o0ci47
2025-07-28 20:31:23,718 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:23,718 - INFO -   - 音频时长: 2.29秒
2025-07-28 20:31:23,718 - INFO -   - 视频时长: 2.34秒
2025-07-28 20:31:23,718 - INFO -   - 时长差异: 0.06秒 (2.49%)
2025-07-28 20:31:23,718 - INFO - 
----- 处理字幕 #29 的方案 #2 -----
2025-07-28 20:31:23,718 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-28 20:31:23,719 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkutmkjxz
2025-07-28 20:31:23,719 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2276.mp4 (确认存在: True)
2025-07-28 20:31:23,719 - INFO - 添加场景ID=2276，时长=1.76秒，累计时长=1.76秒
2025-07-28 20:31:23,719 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2278.mp4 (确认存在: True)
2025-07-28 20:31:23,719 - INFO - 添加场景ID=2278，时长=1.84秒，累计时长=3.60秒
2025-07-28 20:31:23,719 - INFO - 场景总时长(3.60秒)已达到音频时长(2.29秒)的1.5倍，停止添加场景
2025-07-28 20:31:23,719 - INFO - 准备合并 2 个场景文件，总时长约 3.60秒
2025-07-28 20:31:23,720 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2276.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2278.mp4'

2025-07-28 20:31:23,720 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkutmkjxz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkutmkjxz\temp_combined.mp4
2025-07-28 20:31:23,863 - INFO - 合并后的视频时长: 3.65秒，目标音频时长: 2.29秒
2025-07-28 20:31:23,864 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkutmkjxz\temp_combined.mp4 -ss 0 -to 2.286 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-28 20:31:24,078 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:24,079 - INFO - 目标音频时长: 2.29秒
2025-07-28 20:31:24,079 - INFO - 实际视频时长: 2.34秒
2025-07-28 20:31:24,079 - INFO - 时长差异: 0.06秒 (2.49%)
2025-07-28 20:31:24,079 - INFO - ==========================================
2025-07-28 20:31:24,079 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:24,079 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-28 20:31:24,079 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkutmkjxz
2025-07-28 20:31:24,120 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:24,120 - INFO -   - 音频时长: 2.29秒
2025-07-28 20:31:24,120 - INFO -   - 视频时长: 2.34秒
2025-07-28 20:31:24,120 - INFO -   - 时长差异: 0.06秒 (2.49%)
2025-07-28 20:31:24,120 - INFO - 
字幕 #29 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:24,120 - INFO - 生成的视频文件:
2025-07-28 20:31:24,120 - INFO -   1. F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-07-28 20:31:24,120 - INFO -   2. F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-07-28 20:31:24,120 - INFO - ========== 字幕 #29 处理结束 ==========

