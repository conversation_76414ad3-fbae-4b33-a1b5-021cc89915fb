2025-07-28 20:31:06,464 - INFO - ========== 字幕 #10 处理开始 ==========
2025-07-28 20:31:06,464 - INFO - 字幕内容: 但父亲的伤势依然严重，后续每月还需十两银子的药钱。
2025-07-28 20:31:06,464 - INFO - 字幕序号: [222, 224]
2025-07-28 20:31:06,464 - INFO - 音频文件详情:
2025-07-28 20:31:06,464 - INFO -   - 路径: output\10.wav
2025-07-28 20:31:06,464 - INFO -   - 时长: 3.92秒
2025-07-28 20:31:06,465 - INFO -   - 验证音频时长: 3.92秒
2025-07-28 20:31:06,465 - INFO - 字幕时间戳信息:
2025-07-28 20:31:06,465 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:06,465 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:06,465 - INFO -   - 根据生成的音频时长(3.92秒)已调整字幕时间戳
2025-07-28 20:31:06,465 - INFO - ========== 开始为字幕 #10 生成 6 套场景方案 ==========
2025-07-28 20:31:06,465 - INFO - 开始查找字幕序号 [222, 224] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:06,465 - INFO - 找到related_overlap场景: scene_id=264, 字幕#222
2025-07-28 20:31:06,465 - INFO - 找到related_overlap场景: scene_id=266, 字幕#222
2025-07-28 20:31:06,465 - INFO - 找到related_overlap场景: scene_id=268, 字幕#224
2025-07-28 20:31:06,465 - INFO - 找到related_overlap场景: scene_id=269, 字幕#224
2025-07-28 20:31:06,466 - INFO - 字幕 #222 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:06,466 - INFO - 字幕 #224 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:06,466 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:06,466 - INFO - 开始生成方案 #1
2025-07-28 20:31:06,466 - INFO - 方案 #1: 为字幕#222选择初始化overlap场景id=264
2025-07-28 20:31:06,466 - INFO - 方案 #1: 为字幕#224选择初始化overlap场景id=269
2025-07-28 20:31:06,466 - INFO - 方案 #1: 初始选择后，当前总时长=2.20秒
2025-07-28 20:31:06,466 - INFO - 方案 #1: 额外添加overlap场景id=266, 当前总时长=3.16秒
2025-07-28 20:31:06,466 - INFO - 方案 #1: 额外添加overlap场景id=268, 当前总时长=4.44秒
2025-07-28 20:31:06,466 - INFO - 方案 #1: 额外between选择后，当前总时长=4.44秒
2025-07-28 20:31:06,466 - INFO - 方案 #1: 场景总时长(4.44秒)大于音频时长(3.92秒)，需要裁剪
2025-07-28 20:31:06,467 - INFO - 调整前总时长: 4.44秒, 目标时长: 3.92秒
2025-07-28 20:31:06,467 - INFO - 需要裁剪 0.52秒
2025-07-28 20:31:06,467 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:06,467 - INFO - 裁剪场景ID=268：从1.28秒裁剪至1.00秒
2025-07-28 20:31:06,467 - INFO - 裁剪场景ID=269：从1.12秒裁剪至1.00秒
2025-07-28 20:31:06,467 - INFO - 裁剪场景ID=264：从1.08秒裁剪至1.00秒
2025-07-28 20:31:06,467 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.04秒
2025-07-28 20:31:06,467 - INFO - 移除场景ID=266，时长=0.96秒
2025-07-28 20:31:06,467 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.92秒
2025-07-28 20:31:06,467 - INFO - 方案 #1 调整/填充后最终总时长: 3.00秒
2025-07-28 20:31:06,467 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:06,467 - INFO - 开始生成方案 #2
2025-07-28 20:31:06,467 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,467 - INFO - 开始生成方案 #3
2025-07-28 20:31:06,467 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,467 - INFO - 开始生成方案 #4
2025-07-28 20:31:06,467 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,467 - INFO - 开始生成方案 #5
2025-07-28 20:31:06,467 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,467 - INFO - 开始生成方案 #6
2025-07-28 20:31:06,467 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:06,467 - INFO - ========== 字幕 #10 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:06,467 - INFO - 
----- 处理字幕 #10 的方案 #1 -----
2025-07-28 20:31:06,467 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-28 20:31:06,467 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpakoz0a9k
2025-07-28 20:31:06,468 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\264.mp4 (确认存在: True)
2025-07-28 20:31:06,468 - INFO - 添加场景ID=264，时长=1.08秒，累计时长=1.08秒
2025-07-28 20:31:06,468 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\269.mp4 (确认存在: True)
2025-07-28 20:31:06,468 - INFO - 添加场景ID=269，时长=1.12秒，累计时长=2.20秒
2025-07-28 20:31:06,468 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\268.mp4 (确认存在: True)
2025-07-28 20:31:06,468 - INFO - 添加场景ID=268，时长=1.28秒，累计时长=3.48秒
2025-07-28 20:31:06,468 - INFO - 准备合并 3 个场景文件，总时长约 3.48秒
2025-07-28 20:31:06,468 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/264.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/269.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/268.mp4'

2025-07-28 20:31:06,468 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpakoz0a9k\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpakoz0a9k\temp_combined.mp4
2025-07-28 20:31:06,623 - INFO - 合并后的视频时长: 3.55秒，目标音频时长: 3.92秒
2025-07-28 20:31:06,623 - ERROR - 合并后的视频时长(3.55秒)小于音频时长(3.92秒)，无法进行精确裁剪
2025-07-28 20:31:06,623 - WARNING - 将直接使用合并后的视频，可能导致音视频不同步
2025-07-28 20:31:06,686 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:06,686 - INFO - 目标音频时长: 3.92秒
2025-07-28 20:31:06,686 - INFO - 实际视频时长: 3.55秒
2025-07-28 20:31:06,686 - INFO - 时长差异: 0.37秒 (9.42%)
2025-07-28 20:31:06,686 - INFO - ==========================================
2025-07-28 20:31:06,686 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:06,686 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-28 20:31:06,687 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpakoz0a9k
2025-07-28 20:31:06,749 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:06,749 - INFO -   - 音频时长: 3.92秒
2025-07-28 20:31:06,749 - INFO -   - 视频时长: 3.55秒
2025-07-28 20:31:06,749 - INFO -   - 时长差异: 0.37秒 (9.42%)
2025-07-28 20:31:06,749 - INFO - 
字幕 #10 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:06,749 - INFO - 生成的视频文件:
2025-07-28 20:31:06,749 - INFO -   1. F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-07-28 20:31:06,750 - INFO - ========== 字幕 #10 处理结束 ==========

