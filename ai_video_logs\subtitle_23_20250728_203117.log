2025-07-28 20:31:17,379 - INFO - ========== 字幕 #23 处理开始 ==========
2025-07-28 20:31:17,379 - INFO - 字幕内容: 刚刚还豪言壮语的二伯，此刻却第一个逃跑，丑态毕露。
2025-07-28 20:31:17,379 - INFO - 字幕序号: [1936, 1938]
2025-07-28 20:31:17,379 - INFO - 音频文件详情:
2025-07-28 20:31:17,379 - INFO -   - 路径: output\23.wav
2025-07-28 20:31:17,379 - INFO -   - 时长: 2.92秒
2025-07-28 20:31:17,379 - INFO -   - 验证音频时长: 2.92秒
2025-07-28 20:31:17,379 - INFO - 字幕时间戳信息:
2025-07-28 20:31:17,379 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:17,380 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:17,380 - INFO -   - 根据生成的音频时长(2.92秒)已调整字幕时间戳
2025-07-28 20:31:17,380 - INFO - ========== 开始为字幕 #23 生成 6 套场景方案 ==========
2025-07-28 20:31:17,380 - INFO - 开始查找字幕序号 [1936, 1938] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:17,380 - INFO - 找到related_overlap场景: scene_id=2007, 字幕#1936
2025-07-28 20:31:17,380 - INFO - 找到related_overlap场景: scene_id=2010, 字幕#1938
2025-07-28 20:31:17,381 - INFO - 字幕 #1936 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:17,381 - INFO - 字幕 #1938 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:17,381 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:17,381 - INFO - 开始生成方案 #1
2025-07-28 20:31:17,381 - INFO - 方案 #1: 为字幕#1936选择初始化overlap场景id=2007
2025-07-28 20:31:17,381 - INFO - 方案 #1: 为字幕#1938选择初始化overlap场景id=2010
2025-07-28 20:31:17,381 - INFO - 方案 #1: 初始选择后，当前总时长=3.64秒
2025-07-28 20:31:17,381 - INFO - 方案 #1: 额外between选择后，当前总时长=3.64秒
2025-07-28 20:31:17,381 - INFO - 方案 #1: 场景总时长(3.64秒)大于音频时长(2.92秒)，需要裁剪
2025-07-28 20:31:17,381 - INFO - 调整前总时长: 3.64秒, 目标时长: 2.92秒
2025-07-28 20:31:17,381 - INFO - 需要裁剪 0.72秒
2025-07-28 20:31:17,381 - INFO - 裁剪最长场景ID=2007：从2.04秒裁剪至1.32秒
2025-07-28 20:31:17,381 - INFO - 调整后总时长: 2.92秒，与目标时长差异: 0.00秒
2025-07-28 20:31:17,381 - INFO - 方案 #1 调整/填充后最终总时长: 2.92秒
2025-07-28 20:31:17,381 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:17,381 - INFO - 开始生成方案 #2
2025-07-28 20:31:17,381 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:17,381 - INFO - 开始生成方案 #3
2025-07-28 20:31:17,381 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:17,381 - INFO - 开始生成方案 #4
2025-07-28 20:31:17,381 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:17,381 - INFO - 开始生成方案 #5
2025-07-28 20:31:17,381 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:17,381 - INFO - 开始生成方案 #6
2025-07-28 20:31:17,381 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:17,381 - INFO - ========== 字幕 #23 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:17,382 - INFO - 
----- 处理字幕 #23 的方案 #1 -----
2025-07-28 20:31:17,382 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-28 20:31:17,382 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqpv8zb9p
2025-07-28 20:31:17,382 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2007.mp4 (确认存在: True)
2025-07-28 20:31:17,382 - INFO - 添加场景ID=2007，时长=2.04秒，累计时长=2.04秒
2025-07-28 20:31:17,382 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2010.mp4 (确认存在: True)
2025-07-28 20:31:17,382 - INFO - 添加场景ID=2010，时长=1.60秒，累计时长=3.64秒
2025-07-28 20:31:17,382 - INFO - 准备合并 2 个场景文件，总时长约 3.64秒
2025-07-28 20:31:17,382 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2007.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2010.mp4'

2025-07-28 20:31:17,382 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqpv8zb9p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqpv8zb9p\temp_combined.mp4
2025-07-28 20:31:17,506 - INFO - 合并后的视频时长: 3.69秒，目标音频时长: 2.92秒
2025-07-28 20:31:17,506 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqpv8zb9p\temp_combined.mp4 -ss 0 -to 2.922 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-28 20:31:17,760 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:17,760 - INFO - 目标音频时长: 2.92秒
2025-07-28 20:31:17,760 - INFO - 实际视频时长: 2.98秒
2025-07-28 20:31:17,760 - INFO - 时长差异: 0.06秒 (2.09%)
2025-07-28 20:31:17,760 - INFO - ==========================================
2025-07-28 20:31:17,760 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:17,760 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-28 20:31:17,761 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqpv8zb9p
2025-07-28 20:31:17,804 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:17,804 - INFO -   - 音频时长: 2.92秒
2025-07-28 20:31:17,804 - INFO -   - 视频时长: 2.98秒
2025-07-28 20:31:17,804 - INFO -   - 时长差异: 0.06秒 (2.09%)
2025-07-28 20:31:17,805 - INFO - 
字幕 #23 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:17,805 - INFO - 生成的视频文件:
2025-07-28 20:31:17,805 - INFO -   1. F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-07-28 20:31:17,805 - INFO - ========== 字幕 #23 处理结束 ==========

