2025-07-28 20:31:30,498 - INFO - ========== 字幕 #38 处理开始 ==========
2025-07-28 20:31:30,498 - INFO - 字幕内容: 他不仅带来了整个太医院为女孩的父亲诊治，更承诺定能将其救回。
2025-07-28 20:31:30,498 - INFO - 字幕序号: [3253, 3257]
2025-07-28 20:31:30,498 - INFO - 音频文件详情:
2025-07-28 20:31:30,498 - INFO -   - 路径: output\38.wav
2025-07-28 20:31:30,498 - INFO -   - 时长: 3.13秒
2025-07-28 20:31:30,498 - INFO -   - 验证音频时长: 3.13秒
2025-07-28 20:31:30,498 - INFO - 字幕时间戳信息:
2025-07-28 20:31:30,498 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:30,498 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:30,498 - INFO -   - 根据生成的音频时长(3.13秒)已调整字幕时间戳
2025-07-28 20:31:30,498 - INFO - ========== 开始为字幕 #38 生成 6 套场景方案 ==========
2025-07-28 20:31:30,498 - INFO - 开始查找字幕序号 [3253, 3257] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:30,499 - INFO - 找到related_overlap场景: scene_id=3415, 字幕#3253
2025-07-28 20:31:30,499 - INFO - 找到related_overlap场景: scene_id=3418, 字幕#3257
2025-07-28 20:31:30,499 - INFO - 找到related_overlap场景: scene_id=3419, 字幕#3257
2025-07-28 20:31:30,500 - INFO - 字幕 #3253 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:30,500 - INFO - 字幕 #3257 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:30,500 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:30,500 - INFO - 开始生成方案 #1
2025-07-28 20:31:30,500 - INFO - 方案 #1: 为字幕#3253选择初始化overlap场景id=3415
2025-07-28 20:31:30,500 - INFO - 方案 #1: 为字幕#3257选择初始化overlap场景id=3419
2025-07-28 20:31:30,500 - INFO - 方案 #1: 初始选择后，当前总时长=2.60秒
2025-07-28 20:31:30,500 - INFO - 方案 #1: 额外添加overlap场景id=3418, 当前总时长=3.56秒
2025-07-28 20:31:30,500 - INFO - 方案 #1: 额外between选择后，当前总时长=3.56秒
2025-07-28 20:31:30,500 - INFO - 方案 #1: 场景总时长(3.56秒)大于音频时长(3.13秒)，需要裁剪
2025-07-28 20:31:30,500 - INFO - 调整前总时长: 3.56秒, 目标时长: 3.13秒
2025-07-28 20:31:30,500 - INFO - 需要裁剪 0.42秒
2025-07-28 20:31:30,500 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:30,500 - INFO - 裁剪场景ID=3415：从1.40秒裁剪至1.00秒
2025-07-28 20:31:30,500 - INFO - 裁剪场景ID=3419：从1.20秒裁剪至1.18秒
2025-07-28 20:31:30,500 - INFO - 调整后总时长: 3.13秒，与目标时长差异: 0.00秒
2025-07-28 20:31:30,500 - INFO - 方案 #1 调整/填充后最终总时长: 3.13秒
2025-07-28 20:31:30,500 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:30,500 - INFO - 开始生成方案 #2
2025-07-28 20:31:30,500 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,500 - INFO - 开始生成方案 #3
2025-07-28 20:31:30,500 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,500 - INFO - 开始生成方案 #4
2025-07-28 20:31:30,500 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,500 - INFO - 开始生成方案 #5
2025-07-28 20:31:30,500 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,500 - INFO - 开始生成方案 #6
2025-07-28 20:31:30,500 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,500 - INFO - ========== 字幕 #38 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:30,500 - INFO - 
----- 处理字幕 #38 的方案 #1 -----
2025-07-28 20:31:30,500 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-28 20:31:30,501 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjfs8pmlz
2025-07-28 20:31:30,501 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3415.mp4 (确认存在: True)
2025-07-28 20:31:30,501 - INFO - 添加场景ID=3415，时长=1.40秒，累计时长=1.40秒
2025-07-28 20:31:30,501 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3419.mp4 (确认存在: True)
2025-07-28 20:31:30,501 - INFO - 添加场景ID=3419，时长=1.20秒，累计时长=2.60秒
2025-07-28 20:31:30,501 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3418.mp4 (确认存在: True)
2025-07-28 20:31:30,501 - INFO - 添加场景ID=3418，时长=0.96秒，累计时长=3.56秒
2025-07-28 20:31:30,501 - INFO - 准备合并 3 个场景文件，总时长约 3.56秒
2025-07-28 20:31:30,502 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3415.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3419.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3418.mp4'

2025-07-28 20:31:30,502 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjfs8pmlz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjfs8pmlz\temp_combined.mp4
2025-07-28 20:31:30,668 - INFO - 合并后的视频时长: 3.63秒，目标音频时长: 3.13秒
2025-07-28 20:31:30,668 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjfs8pmlz\temp_combined.mp4 -ss 0 -to 3.135 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-28 20:31:30,934 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:30,934 - INFO - 目标音频时长: 3.13秒
2025-07-28 20:31:30,934 - INFO - 实际视频时长: 3.18秒
2025-07-28 20:31:30,934 - INFO - 时长差异: 0.05秒 (1.53%)
2025-07-28 20:31:30,934 - INFO - ==========================================
2025-07-28 20:31:30,934 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:30,934 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-28 20:31:30,935 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjfs8pmlz
2025-07-28 20:31:30,977 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:30,977 - INFO -   - 音频时长: 3.13秒
2025-07-28 20:31:30,977 - INFO -   - 视频时长: 3.18秒
2025-07-28 20:31:30,977 - INFO -   - 时长差异: 0.05秒 (1.53%)
2025-07-28 20:31:30,977 - INFO - 
字幕 #38 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:30,977 - INFO - 生成的视频文件:
2025-07-28 20:31:30,977 - INFO -   1. F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-07-28 20:31:30,977 - INFO - ========== 字幕 #38 处理结束 ==========

