2025-07-28 20:31:21,836 - INFO - ========== 字幕 #28 处理开始 ==========
2025-07-28 20:31:21,837 - INFO - 字幕内容: 结果，两个成年人使出吃奶的力气，也无法撼动黑熊分毫，最终狼狈退场。
2025-07-28 20:31:21,837 - INFO - 字幕序号: [2041, 2046]
2025-07-28 20:31:21,837 - INFO - 音频文件详情:
2025-07-28 20:31:21,837 - INFO -   - 路径: output\28.wav
2025-07-28 20:31:21,837 - INFO -   - 时长: 3.52秒
2025-07-28 20:31:21,837 - INFO -   - 验证音频时长: 3.52秒
2025-07-28 20:31:21,837 - INFO - 字幕时间戳信息:
2025-07-28 20:31:21,837 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:21,837 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:21,837 - INFO -   - 根据生成的音频时长(3.52秒)已调整字幕时间戳
2025-07-28 20:31:21,837 - INFO - ========== 开始为字幕 #28 生成 6 套场景方案 ==========
2025-07-28 20:31:21,837 - INFO - 开始查找字幕序号 [2041, 2046] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:21,838 - INFO - 找到related_overlap场景: scene_id=2133, 字幕#2041
2025-07-28 20:31:21,838 - INFO - 找到related_overlap场景: scene_id=2142, 字幕#2046
2025-07-28 20:31:21,838 - INFO - 找到related_overlap场景: scene_id=2143, 字幕#2046
2025-07-28 20:31:21,839 - INFO - 找到related_between场景: scene_id=2129, 字幕#2041
2025-07-28 20:31:21,839 - INFO - 找到related_between场景: scene_id=2130, 字幕#2041
2025-07-28 20:31:21,839 - INFO - 找到related_between场景: scene_id=2131, 字幕#2041
2025-07-28 20:31:21,839 - INFO - 找到related_between场景: scene_id=2132, 字幕#2041
2025-07-28 20:31:21,839 - INFO - 字幕 #2041 找到 1 个overlap场景, 4 个between场景
2025-07-28 20:31:21,839 - INFO - 字幕 #2046 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:21,839 - INFO - 共收集 3 个未使用的overlap场景和 4 个未使用的between场景
2025-07-28 20:31:21,839 - INFO - 开始生成方案 #1
2025-07-28 20:31:21,839 - INFO - 方案 #1: 为字幕#2041选择初始化overlap场景id=2133
2025-07-28 20:31:21,839 - INFO - 方案 #1: 为字幕#2046选择初始化overlap场景id=2142
2025-07-28 20:31:21,839 - INFO - 方案 #1: 初始选择后，当前总时长=3.04秒
2025-07-28 20:31:21,839 - INFO - 方案 #1: 额外添加overlap场景id=2143, 当前总时长=4.20秒
2025-07-28 20:31:21,839 - INFO - 方案 #1: 额外between选择后，当前总时长=4.20秒
2025-07-28 20:31:21,839 - INFO - 方案 #1: 场景总时长(4.20秒)大于音频时长(3.52秒)，需要裁剪
2025-07-28 20:31:21,839 - INFO - 调整前总时长: 4.20秒, 目标时长: 3.52秒
2025-07-28 20:31:21,839 - INFO - 需要裁剪 0.68秒
2025-07-28 20:31:21,839 - INFO - 裁剪最长场景ID=2142：从1.76秒裁剪至1.08秒
2025-07-28 20:31:21,839 - INFO - 调整后总时长: 3.52秒，与目标时长差异: 0.00秒
2025-07-28 20:31:21,839 - INFO - 方案 #1 调整/填充后最终总时长: 3.52秒
2025-07-28 20:31:21,839 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:21,839 - INFO - 开始生成方案 #2
2025-07-28 20:31:21,839 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:21,839 - INFO - 方案 #2: 为字幕#2041选择初始化between场景id=2129
2025-07-28 20:31:21,839 - INFO - 方案 #2: 额外between选择后，当前总时长=2.00秒
2025-07-28 20:31:21,839 - INFO - 方案 #2: 额外添加between场景id=2131, 当前总时长=3.32秒
2025-07-28 20:31:21,839 - INFO - 方案 #2: 额外添加between场景id=2132, 当前总时长=4.20秒
2025-07-28 20:31:21,839 - INFO - 方案 #2: 场景总时长(4.20秒)大于音频时长(3.52秒)，需要裁剪
2025-07-28 20:31:21,839 - INFO - 调整前总时长: 4.20秒, 目标时长: 3.52秒
2025-07-28 20:31:21,839 - INFO - 需要裁剪 0.68秒
2025-07-28 20:31:21,839 - INFO - 裁剪最长场景ID=2129：从2.00秒裁剪至1.32秒
2025-07-28 20:31:21,839 - INFO - 调整后总时长: 3.52秒，与目标时长差异: 0.00秒
2025-07-28 20:31:21,839 - INFO - 方案 #2 调整/填充后最终总时长: 3.52秒
2025-07-28 20:31:21,839 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:21,839 - INFO - 开始生成方案 #3
2025-07-28 20:31:21,839 - INFO - 方案 #3: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:21,839 - INFO - 方案 #3: 为字幕#2041选择初始化between场景id=2130
2025-07-28 20:31:21,839 - INFO - 方案 #3: 额外between选择后，当前总时长=1.08秒
2025-07-28 20:31:21,839 - INFO - 方案 #3: 场景总时长(1.08秒)小于音频时长(3.52秒)，需要延伸填充
2025-07-28 20:31:21,839 - INFO - 方案 #3: 最后一个场景ID: 2130
2025-07-28 20:31:21,839 - INFO - 方案 #3: 找到最后一个场景在原始列表中的索引: 2129
2025-07-28 20:31:21,840 - INFO - 方案 #3: 需要填充时长: 2.44秒
2025-07-28 20:31:21,840 - INFO - 方案 #3: 跳过已使用的场景: scene_id=2131
2025-07-28 20:31:21,840 - INFO - 方案 #3: 跳过已使用的场景: scene_id=2132
2025-07-28 20:31:21,840 - INFO - 方案 #3: 跳过已使用的场景: scene_id=2133
2025-07-28 20:31:21,840 - INFO - 方案 #3: 追加场景 scene_id=2134 (完整时长 1.20秒)
2025-07-28 20:31:21,840 - INFO - 方案 #3: 追加场景 scene_id=2135 (完整时长 1.12秒)
2025-07-28 20:31:21,840 - INFO - 方案 #3: 追加场景 scene_id=2136 (裁剪至 0.12秒)
2025-07-28 20:31:21,840 - INFO - 方案 #3: 成功填充至目标时长
2025-07-28 20:31:21,840 - INFO - 方案 #3 调整/填充后最终总时长: 3.52秒
2025-07-28 20:31:21,840 - INFO - 方案 #3 添加到方案列表
2025-07-28 20:31:21,840 - INFO - 开始生成方案 #4
2025-07-28 20:31:21,840 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:21,840 - INFO - 开始生成方案 #5
2025-07-28 20:31:21,840 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:21,840 - INFO - 开始生成方案 #6
2025-07-28 20:31:21,840 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:21,840 - INFO - ========== 字幕 #28 的 3 套有效场景方案生成完成 ==========
2025-07-28 20:31:21,840 - INFO - 
----- 处理字幕 #28 的方案 #1 -----
2025-07-28 20:31:21,840 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-28 20:31:21,840 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4vb49k61
2025-07-28 20:31:21,841 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2133.mp4 (确认存在: True)
2025-07-28 20:31:21,841 - INFO - 添加场景ID=2133，时长=1.28秒，累计时长=1.28秒
2025-07-28 20:31:21,841 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2142.mp4 (确认存在: True)
2025-07-28 20:31:21,841 - INFO - 添加场景ID=2142，时长=1.76秒，累计时长=3.04秒
2025-07-28 20:31:21,841 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2143.mp4 (确认存在: True)
2025-07-28 20:31:21,841 - INFO - 添加场景ID=2143，时长=1.16秒，累计时长=4.20秒
2025-07-28 20:31:21,841 - INFO - 准备合并 3 个场景文件，总时长约 4.20秒
2025-07-28 20:31:21,841 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2133.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2142.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2143.mp4'

2025-07-28 20:31:21,841 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp4vb49k61\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp4vb49k61\temp_combined.mp4
2025-07-28 20:31:21,994 - INFO - 合并后的视频时长: 4.27秒，目标音频时长: 3.52秒
2025-07-28 20:31:21,994 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp4vb49k61\temp_combined.mp4 -ss 0 -to 3.52 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-28 20:31:22,273 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:22,273 - INFO - 目标音频时长: 3.52秒
2025-07-28 20:31:22,273 - INFO - 实际视频时长: 3.54秒
2025-07-28 20:31:22,273 - INFO - 时长差异: 0.02秒 (0.66%)
2025-07-28 20:31:22,273 - INFO - ==========================================
2025-07-28 20:31:22,273 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:22,273 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-28 20:31:22,274 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4vb49k61
2025-07-28 20:31:22,315 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:22,315 - INFO -   - 音频时长: 3.52秒
2025-07-28 20:31:22,316 - INFO -   - 视频时长: 3.54秒
2025-07-28 20:31:22,316 - INFO -   - 时长差异: 0.02秒 (0.66%)
2025-07-28 20:31:22,316 - INFO - 
----- 处理字幕 #28 的方案 #2 -----
2025-07-28 20:31:22,316 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-28 20:31:22,316 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpoo2x5wxy
2025-07-28 20:31:22,317 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2129.mp4 (确认存在: True)
2025-07-28 20:31:22,317 - INFO - 添加场景ID=2129，时长=2.00秒，累计时长=2.00秒
2025-07-28 20:31:22,317 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2131.mp4 (确认存在: True)
2025-07-28 20:31:22,317 - INFO - 添加场景ID=2131，时长=1.32秒，累计时长=3.32秒
2025-07-28 20:31:22,317 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2132.mp4 (确认存在: True)
2025-07-28 20:31:22,317 - INFO - 添加场景ID=2132，时长=0.88秒，累计时长=4.20秒
2025-07-28 20:31:22,317 - INFO - 准备合并 3 个场景文件，总时长约 4.20秒
2025-07-28 20:31:22,317 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2129.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2131.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2132.mp4'

2025-07-28 20:31:22,317 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpoo2x5wxy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpoo2x5wxy\temp_combined.mp4
2025-07-28 20:31:22,477 - INFO - 合并后的视频时长: 4.27秒，目标音频时长: 3.52秒
2025-07-28 20:31:22,477 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpoo2x5wxy\temp_combined.mp4 -ss 0 -to 3.52 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-28 20:31:22,780 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:22,780 - INFO - 目标音频时长: 3.52秒
2025-07-28 20:31:22,780 - INFO - 实际视频时长: 3.54秒
2025-07-28 20:31:22,780 - INFO - 时长差异: 0.02秒 (0.66%)
2025-07-28 20:31:22,780 - INFO - ==========================================
2025-07-28 20:31:22,780 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:22,780 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-28 20:31:22,781 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpoo2x5wxy
2025-07-28 20:31:22,821 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:22,821 - INFO -   - 音频时长: 3.52秒
2025-07-28 20:31:22,821 - INFO -   - 视频时长: 3.54秒
2025-07-28 20:31:22,821 - INFO -   - 时长差异: 0.02秒 (0.66%)
2025-07-28 20:31:22,821 - INFO - 
----- 处理字幕 #28 的方案 #3 -----
2025-07-28 20:31:22,821 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-28 20:31:22,821 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0f8sxhzm
2025-07-28 20:31:22,822 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2130.mp4 (确认存在: True)
2025-07-28 20:31:22,822 - INFO - 添加场景ID=2130，时长=1.08秒，累计时长=1.08秒
2025-07-28 20:31:22,822 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2134.mp4 (确认存在: True)
2025-07-28 20:31:22,822 - INFO - 添加场景ID=2134，时长=1.20秒，累计时长=2.28秒
2025-07-28 20:31:22,822 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2135.mp4 (确认存在: True)
2025-07-28 20:31:22,822 - INFO - 添加场景ID=2135，时长=1.12秒，累计时长=3.40秒
2025-07-28 20:31:22,822 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2136.mp4 (确认存在: True)
2025-07-28 20:31:22,822 - INFO - 添加场景ID=2136，时长=2.20秒，累计时长=5.60秒
2025-07-28 20:31:22,822 - INFO - 场景总时长(5.60秒)已达到音频时长(3.52秒)的1.5倍，停止添加场景
2025-07-28 20:31:22,822 - INFO - 准备合并 4 个场景文件，总时长约 5.60秒
2025-07-28 20:31:22,822 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2130.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2134.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2135.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2136.mp4'

2025-07-28 20:31:22,822 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0f8sxhzm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0f8sxhzm\temp_combined.mp4
2025-07-28 20:31:22,960 - INFO - 合并后的视频时长: 5.69秒，目标音频时长: 3.52秒
2025-07-28 20:31:22,960 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0f8sxhzm\temp_combined.mp4 -ss 0 -to 3.52 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-28 20:31:23,258 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:23,258 - INFO - 目标音频时长: 3.52秒
2025-07-28 20:31:23,258 - INFO - 实际视频时长: 3.54秒
2025-07-28 20:31:23,258 - INFO - 时长差异: 0.02秒 (0.66%)
2025-07-28 20:31:23,258 - INFO - ==========================================
2025-07-28 20:31:23,258 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:23,258 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-28 20:31:23,258 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0f8sxhzm
2025-07-28 20:31:23,301 - INFO - 方案 #3 处理完成:
2025-07-28 20:31:23,301 - INFO -   - 音频时长: 3.52秒
2025-07-28 20:31:23,301 - INFO -   - 视频时长: 3.54秒
2025-07-28 20:31:23,301 - INFO -   - 时长差异: 0.02秒 (0.66%)
2025-07-28 20:31:23,301 - INFO - 
字幕 #28 处理完成，成功生成 3/3 套方案
2025-07-28 20:31:23,301 - INFO - 生成的视频文件:
2025-07-28 20:31:23,301 - INFO -   1. F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-07-28 20:31:23,301 - INFO -   2. F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-07-28 20:31:23,301 - INFO -   3. F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-07-28 20:31:23,301 - INFO - ========== 字幕 #28 处理结束 ==========

