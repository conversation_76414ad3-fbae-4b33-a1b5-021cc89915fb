2025-07-28 20:31:20,019 - INFO - ========== 字幕 #25 处理开始 ==========
2025-07-28 20:31:20,019 - INFO - 字幕内容: 村民们彻底被女孩的神力折服，这个瘦弱的女孩，成了全村的英雄。
2025-07-28 20:31:20,019 - INFO - 字幕序号: [1951, 1954]
2025-07-28 20:31:20,019 - INFO - 音频文件详情:
2025-07-28 20:31:20,019 - INFO -   - 路径: output\25.wav
2025-07-28 20:31:20,019 - INFO -   - 时长: 3.25秒
2025-07-28 20:31:20,020 - INFO -   - 验证音频时长: 3.25秒
2025-07-28 20:31:20,020 - INFO - 字幕时间戳信息:
2025-07-28 20:31:20,020 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:20,020 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:20,020 - INFO -   - 根据生成的音频时长(3.25秒)已调整字幕时间戳
2025-07-28 20:31:20,020 - INFO - ========== 开始为字幕 #25 生成 6 套场景方案 ==========
2025-07-28 20:31:20,020 - INFO - 开始查找字幕序号 [1951, 1954] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:20,021 - INFO - 找到related_overlap场景: scene_id=2038, 字幕#1951
2025-07-28 20:31:20,021 - INFO - 找到related_overlap场景: scene_id=2040, 字幕#1954
2025-07-28 20:31:20,021 - INFO - 找到related_overlap场景: scene_id=2041, 字幕#1954
2025-07-28 20:31:20,022 - INFO - 找到related_between场景: scene_id=2042, 字幕#1954
2025-07-28 20:31:20,022 - INFO - 找到related_between场景: scene_id=2043, 字幕#1954
2025-07-28 20:31:20,022 - INFO - 找到related_between场景: scene_id=2044, 字幕#1954
2025-07-28 20:31:20,022 - INFO - 字幕 #1951 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:20,022 - INFO - 字幕 #1954 找到 2 个overlap场景, 3 个between场景
2025-07-28 20:31:20,022 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-07-28 20:31:20,022 - INFO - 开始生成方案 #1
2025-07-28 20:31:20,022 - INFO - 方案 #1: 为字幕#1951选择初始化overlap场景id=2038
2025-07-28 20:31:20,022 - INFO - 方案 #1: 为字幕#1954选择初始化overlap场景id=2041
2025-07-28 20:31:20,022 - INFO - 方案 #1: 初始选择后，当前总时长=2.48秒
2025-07-28 20:31:20,022 - INFO - 方案 #1: 额外添加overlap场景id=2040, 当前总时长=4.24秒
2025-07-28 20:31:20,024 - INFO - 方案 #1: 额外between选择后，当前总时长=4.24秒
2025-07-28 20:31:20,024 - INFO - 方案 #1: 场景总时长(4.24秒)大于音频时长(3.25秒)，需要裁剪
2025-07-28 20:31:20,024 - INFO - 调整前总时长: 4.24秒, 目标时长: 3.25秒
2025-07-28 20:31:20,024 - INFO - 需要裁剪 0.98秒
2025-07-28 20:31:20,024 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:20,024 - INFO - 裁剪场景ID=2040：从1.76秒裁剪至1.00秒
2025-07-28 20:31:20,024 - INFO - 裁剪场景ID=2038：从1.52秒裁剪至1.30秒
2025-07-28 20:31:20,024 - INFO - 调整后总时长: 3.26秒，与目标时长差异: 0.00秒
2025-07-28 20:31:20,024 - INFO - 方案 #1 调整/填充后最终总时长: 3.26秒
2025-07-28 20:31:20,024 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:20,024 - INFO - 开始生成方案 #2
2025-07-28 20:31:20,024 - INFO - 方案 #2: 初始选择后，当前总时长=0.00秒
2025-07-28 20:31:20,024 - INFO - 方案 #2: 为字幕#1954选择初始化between场景id=2042
2025-07-28 20:31:20,024 - INFO - 方案 #2: 额外between选择后，当前总时长=1.48秒
2025-07-28 20:31:20,024 - INFO - 方案 #2: 额外添加between场景id=2043, 当前总时长=2.48秒
2025-07-28 20:31:20,024 - INFO - 方案 #2: 额外添加between场景id=2044, 当前总时长=4.04秒
2025-07-28 20:31:20,024 - INFO - 方案 #2: 场景总时长(4.04秒)大于音频时长(3.25秒)，需要裁剪
2025-07-28 20:31:20,024 - INFO - 调整前总时长: 4.04秒, 目标时长: 3.25秒
2025-07-28 20:31:20,024 - INFO - 需要裁剪 0.78秒
2025-07-28 20:31:20,024 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:20,024 - INFO - 裁剪场景ID=2044：从1.56秒裁剪至1.00秒
2025-07-28 20:31:20,024 - INFO - 裁剪场景ID=2042：从1.48秒裁剪至1.26秒
2025-07-28 20:31:20,024 - INFO - 调整后总时长: 3.26秒，与目标时长差异: 0.00秒
2025-07-28 20:31:20,024 - INFO - 方案 #2 调整/填充后最终总时长: 3.26秒
2025-07-28 20:31:20,024 - INFO - 方案 #2 添加到方案列表
2025-07-28 20:31:20,024 - INFO - 开始生成方案 #3
2025-07-28 20:31:20,024 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:20,024 - INFO - 开始生成方案 #4
2025-07-28 20:31:20,024 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:20,024 - INFO - 开始生成方案 #5
2025-07-28 20:31:20,024 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:20,024 - INFO - 开始生成方案 #6
2025-07-28 20:31:20,024 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:20,024 - INFO - ========== 字幕 #25 的 2 套有效场景方案生成完成 ==========
2025-07-28 20:31:20,024 - INFO - 
----- 处理字幕 #25 的方案 #1 -----
2025-07-28 20:31:20,024 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-28 20:31:20,025 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpde689xr0
2025-07-28 20:31:20,025 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2038.mp4 (确认存在: True)
2025-07-28 20:31:20,025 - INFO - 添加场景ID=2038，时长=1.52秒，累计时长=1.52秒
2025-07-28 20:31:20,026 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2041.mp4 (确认存在: True)
2025-07-28 20:31:20,026 - INFO - 添加场景ID=2041，时长=0.96秒，累计时长=2.48秒
2025-07-28 20:31:20,026 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2040.mp4 (确认存在: True)
2025-07-28 20:31:20,026 - INFO - 添加场景ID=2040，时长=1.76秒，累计时长=4.24秒
2025-07-28 20:31:20,026 - INFO - 准备合并 3 个场景文件，总时长约 4.24秒
2025-07-28 20:31:20,026 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2038.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2041.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2040.mp4'

2025-07-28 20:31:20,026 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpde689xr0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpde689xr0\temp_combined.mp4
2025-07-28 20:31:20,182 - INFO - 合并后的视频时长: 4.31秒，目标音频时长: 3.25秒
2025-07-28 20:31:20,182 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpde689xr0\temp_combined.mp4 -ss 0 -to 3.255 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-28 20:31:20,468 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:20,468 - INFO - 目标音频时长: 3.25秒
2025-07-28 20:31:20,468 - INFO - 实际视频时长: 3.30秒
2025-07-28 20:31:20,468 - INFO - 时长差异: 0.05秒 (1.47%)
2025-07-28 20:31:20,468 - INFO - ==========================================
2025-07-28 20:31:20,468 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:20,468 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-28 20:31:20,469 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpde689xr0
2025-07-28 20:31:20,511 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:20,511 - INFO -   - 音频时长: 3.25秒
2025-07-28 20:31:20,511 - INFO -   - 视频时长: 3.30秒
2025-07-28 20:31:20,511 - INFO -   - 时长差异: 0.05秒 (1.47%)
2025-07-28 20:31:20,511 - INFO - 
----- 处理字幕 #25 的方案 #2 -----
2025-07-28 20:31:20,511 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-28 20:31:20,511 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbvha9g3j
2025-07-28 20:31:20,512 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2042.mp4 (确认存在: True)
2025-07-28 20:31:20,512 - INFO - 添加场景ID=2042，时长=1.48秒，累计时长=1.48秒
2025-07-28 20:31:20,512 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2043.mp4 (确认存在: True)
2025-07-28 20:31:20,512 - INFO - 添加场景ID=2043，时长=1.00秒，累计时长=2.48秒
2025-07-28 20:31:20,512 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2044.mp4 (确认存在: True)
2025-07-28 20:31:20,512 - INFO - 添加场景ID=2044，时长=1.56秒，累计时长=4.04秒
2025-07-28 20:31:20,512 - INFO - 准备合并 3 个场景文件，总时长约 4.04秒
2025-07-28 20:31:20,512 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2042.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2043.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2044.mp4'

2025-07-28 20:31:20,512 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbvha9g3j\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbvha9g3j\temp_combined.mp4
2025-07-28 20:31:20,667 - INFO - 合并后的视频时长: 4.11秒，目标音频时长: 3.25秒
2025-07-28 20:31:20,667 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbvha9g3j\temp_combined.mp4 -ss 0 -to 3.255 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-28 20:31:20,935 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:20,935 - INFO - 目标音频时长: 3.25秒
2025-07-28 20:31:20,935 - INFO - 实际视频时长: 3.30秒
2025-07-28 20:31:20,935 - INFO - 时长差异: 0.05秒 (1.47%)
2025-07-28 20:31:20,935 - INFO - ==========================================
2025-07-28 20:31:20,935 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:20,935 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-28 20:31:20,936 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbvha9g3j
2025-07-28 20:31:20,976 - INFO - 方案 #2 处理完成:
2025-07-28 20:31:20,976 - INFO -   - 音频时长: 3.25秒
2025-07-28 20:31:20,976 - INFO -   - 视频时长: 3.30秒
2025-07-28 20:31:20,976 - INFO -   - 时长差异: 0.05秒 (1.47%)
2025-07-28 20:31:20,976 - INFO - 
字幕 #25 处理完成，成功生成 2/2 套方案
2025-07-28 20:31:20,976 - INFO - 生成的视频文件:
2025-07-28 20:31:20,976 - INFO -   1. F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-07-28 20:31:20,976 - INFO -   2. F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-07-28 20:31:20,976 - INFO - ========== 字幕 #25 处理结束 ==========

