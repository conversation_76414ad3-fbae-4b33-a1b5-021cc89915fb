2025-07-28 20:31:30,007 - INFO - ========== 字幕 #37 处理开始 ==========
2025-07-28 20:31:30,007 - INFO - 字幕内容: 太子当众宣布，女孩是他的救命恩人，任何人不得无礼。
2025-07-28 20:31:30,007 - INFO - 字幕序号: [3247, 3248]
2025-07-28 20:31:30,008 - INFO - 音频文件详情:
2025-07-28 20:31:30,008 - INFO -   - 路径: output\37.wav
2025-07-28 20:31:30,008 - INFO -   - 时长: 3.02秒
2025-07-28 20:31:30,008 - INFO -   - 验证音频时长: 3.02秒
2025-07-28 20:31:30,008 - INFO - 字幕时间戳信息:
2025-07-28 20:31:30,008 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-07-28 20:31:30,008 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-07-28 20:31:30,008 - INFO -   - 根据生成的音频时长(3.02秒)已调整字幕时间戳
2025-07-28 20:31:30,008 - INFO - ========== 开始为字幕 #37 生成 6 套场景方案 ==========
2025-07-28 20:31:30,008 - INFO - 开始查找字幕序号 [3247, 3248] 对应的场景，共有 3562 个场景可选
2025-07-28 20:31:30,009 - INFO - 找到related_overlap场景: scene_id=3410, 字幕#3247
2025-07-28 20:31:30,009 - INFO - 找到related_overlap场景: scene_id=3411, 字幕#3248
2025-07-28 20:31:30,009 - INFO - 找到related_overlap场景: scene_id=3412, 字幕#3248
2025-07-28 20:31:30,010 - INFO - 字幕 #3247 找到 1 个overlap场景, 0 个between场景
2025-07-28 20:31:30,010 - INFO - 字幕 #3248 找到 2 个overlap场景, 0 个between场景
2025-07-28 20:31:30,010 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-07-28 20:31:30,010 - INFO - 开始生成方案 #1
2025-07-28 20:31:30,010 - INFO - 方案 #1: 为字幕#3247选择初始化overlap场景id=3410
2025-07-28 20:31:30,010 - INFO - 方案 #1: 为字幕#3248选择初始化overlap场景id=3411
2025-07-28 20:31:30,010 - INFO - 方案 #1: 初始选择后，当前总时长=2.80秒
2025-07-28 20:31:30,010 - INFO - 方案 #1: 额外添加overlap场景id=3412, 当前总时长=4.72秒
2025-07-28 20:31:30,010 - INFO - 方案 #1: 额外between选择后，当前总时长=4.72秒
2025-07-28 20:31:30,010 - INFO - 方案 #1: 场景总时长(4.72秒)大于音频时长(3.02秒)，需要裁剪
2025-07-28 20:31:30,010 - INFO - 调整前总时长: 4.72秒, 目标时长: 3.02秒
2025-07-28 20:31:30,010 - INFO - 需要裁剪 1.70秒
2025-07-28 20:31:30,010 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-07-28 20:31:30,010 - INFO - 裁剪场景ID=3412：从1.92秒裁剪至1.00秒
2025-07-28 20:31:30,010 - INFO - 裁剪场景ID=3411：从1.72秒裁剪至1.00秒
2025-07-28 20:31:30,010 - INFO - 裁剪场景ID=3410：从1.08秒裁剪至1.02秒
2025-07-28 20:31:30,010 - INFO - 调整后总时长: 3.02秒，与目标时长差异: 0.00秒
2025-07-28 20:31:30,010 - INFO - 方案 #1 调整/填充后最终总时长: 3.02秒
2025-07-28 20:31:30,010 - INFO - 方案 #1 添加到方案列表
2025-07-28 20:31:30,010 - INFO - 开始生成方案 #2
2025-07-28 20:31:30,010 - WARNING - 方案 #2: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,010 - INFO - 开始生成方案 #3
2025-07-28 20:31:30,010 - WARNING - 方案 #3: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,010 - INFO - 开始生成方案 #4
2025-07-28 20:31:30,010 - WARNING - 方案 #4: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,010 - INFO - 开始生成方案 #5
2025-07-28 20:31:30,010 - WARNING - 方案 #5: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,010 - INFO - 开始生成方案 #6
2025-07-28 20:31:30,010 - WARNING - 方案 #6: 没有可用的未使用的场景，跳过此方案生成。
2025-07-28 20:31:30,010 - INFO - ========== 字幕 #37 的 1 套有效场景方案生成完成 ==========
2025-07-28 20:31:30,010 - INFO - 
----- 处理字幕 #37 的方案 #1 -----
2025-07-28 20:31:30,010 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-28 20:31:30,010 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3sj68sds
2025-07-28 20:31:30,011 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3410.mp4 (确认存在: True)
2025-07-28 20:31:30,011 - INFO - 添加场景ID=3410，时长=1.08秒，累计时长=1.08秒
2025-07-28 20:31:30,011 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3411.mp4 (确认存在: True)
2025-07-28 20:31:30,011 - INFO - 添加场景ID=3411，时长=1.72秒，累计时长=2.80秒
2025-07-28 20:31:30,011 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3412.mp4 (确认存在: True)
2025-07-28 20:31:30,011 - INFO - 添加场景ID=3412，时长=1.92秒，累计时长=4.72秒
2025-07-28 20:31:30,011 - INFO - 场景总时长(4.72秒)已达到音频时长(3.02秒)的1.5倍，停止添加场景
2025-07-28 20:31:30,011 - INFO - 准备合并 3 个场景文件，总时长约 4.72秒
2025-07-28 20:31:30,011 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3410.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3411.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3412.mp4'

2025-07-28 20:31:30,011 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3sj68sds\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3sj68sds\temp_combined.mp4
2025-07-28 20:31:30,182 - INFO - 合并后的视频时长: 4.79秒，目标音频时长: 3.02秒
2025-07-28 20:31:30,182 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3sj68sds\temp_combined.mp4 -ss 0 -to 3.022 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-28 20:31:30,453 - INFO - ============ 视频与音频时长比较 ============
2025-07-28 20:31:30,453 - INFO - 目标音频时长: 3.02秒
2025-07-28 20:31:30,453 - INFO - 实际视频时长: 3.06秒
2025-07-28 20:31:30,453 - INFO - 时长差异: 0.04秒 (1.36%)
2025-07-28 20:31:30,453 - INFO - ==========================================
2025-07-28 20:31:30,453 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-07-28 20:31:30,453 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-28 20:31:30,453 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3sj68sds
2025-07-28 20:31:30,497 - INFO - 方案 #1 处理完成:
2025-07-28 20:31:30,497 - INFO -   - 音频时长: 3.02秒
2025-07-28 20:31:30,497 - INFO -   - 视频时长: 3.06秒
2025-07-28 20:31:30,497 - INFO -   - 时长差异: 0.04秒 (1.36%)
2025-07-28 20:31:30,497 - INFO - 
字幕 #37 处理完成，成功生成 1/1 套方案
2025-07-28 20:31:30,497 - INFO - 生成的视频文件:
2025-07-28 20:31:30,497 - INFO -   1. F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-07-28 20:31:30,497 - INFO - ========== 字幕 #37 处理结束 ==========

